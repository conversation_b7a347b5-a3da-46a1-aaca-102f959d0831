<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PubMed文献检索系统 - 完整使用说明</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            text-align: center;
        }
        
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
        }
        
        h3 {
            color: #2980b9;
            margin-top: 30px;
        }
        
        h4 {
            color: #27ae60;
            margin-top: 25px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            padding: 15px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: #f8d7da;
            padding: 15px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .workflow-step {
            background: #e3f2fd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
        
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
        
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        
        .flowchart-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .qa-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #17a2b8;
        }
        
        .qa-question {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .qa-answer {
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 PubMed文献检索系统 - 完整使用说明</h1>
        
        <div class="highlight">
            <strong>🎉 欢迎使用！</strong> 本工具是一个智能小助手，能帮您自动完成繁琐的PubMed文献检索工作。您只需要提出一个研究问题，它就能为您搜索、筛选、整理文献，并最终生成一份带期刊分区、影响因子和PMC链接的精美Excel报告。
        </div>

        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#features">✨ 工具特色</a></li>
                <li><a href="#workflow">🔄 工作流程图</a></li>
                <li><a href="#installation">🚀 首次安装</a></li>
                <li><a href="#usage">📖 开始使用</a></li>
                <li><a href="#models">🤖 AI模型配置</a></li>
                <li><a href="#troubleshooting">🛠️ 常见问题</a></li>
                <li><a href="#support">📞 技术支持</a></li>
            </ul>
        </div>

        <h2 id="features">✨ 工具特色</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🤖 AI智能分析</h4>
                <p>集成Google Gemini模型，智能生成PICO标准和检索式，确保检索的科学性和准确性。</p>
            </div>
            <div class="feature-card">
                <h4>🔍 自动搜索</h4>
                <p>自动搜索PubMed数据库，智能筛选相关文献，基于循证医学PICO标准。</p>
            </div>
            <div class="feature-card">
                <h4>📊 数据丰富</h4>
                <p>自动添加期刊影响因子、中科院分区信息和PMC链接，提供完整的期刊质量评估。</p>
            </div>
            <div class="feature-card">
                <h4>📋 专业报告</h4>
                <p>生成格式化Excel报告，包含PMCID、PMC链接、超链接、筛选功能和专业排版。</p>
            </div>
            <div class="feature-card">
                <h4>⚡ 高效处理</h4>
                <p>优化性能参数：最多100次循环，目标500篇高质量文献，20线程并发处理。</p>
            </div>
            <div class="feature-card">
                <h4>🔄 智能优化</h4>
                <p>自适应检索策略，根据结果动态调整，平衡召回率和精确率。</p>
            </div>
        </div>

        <h2 id="workflow">🔄 工作流程图</h2>
        
        <div class="flowchart-container">
            <h3>📊 实际工作流程（真实循环逻辑）</h3>
            <div class="mermaid">
                graph TD
                    A[👤 用户输入研究问题] --> B[🤖 生成PICO标准<br/>Gemini 2.5-pro]
                    B --> C[🔍 生成初始检索式<br/>Gemini 2.5-flash-lite]
                    C --> D[📊 初始化循环计数器<br/>attempt = 1]

                    %% 主循环开始
                    D --> E[🌐 PubMed搜索<br/>esearch获取PMID列表]
                    E --> F{📋 找到新PMID?}
                    F -->|否| G[🔄 优化检索式<br/>Gemini重新生成]
                    F -->|是| H[📚 获取前600篇文献详情<br/>efetch POST请求]

                    H --> I[🧵 并发筛选文献<br/>20线程 + Gemini筛选]
                    I --> J[📊 更新PMID缓存<br/>避免重复处理]
                    J --> K[📋 生成/更新Excel报告<br/>累积所有结果]
                    K --> L{📈 累计文献数 ≥ 500?}

                    %% 成功结束条件
                    L -->|是| M[✅ 任务完成<br/>达到目标数量]

                    %% 继续循环条件
                    L -->|否| N{🔁 循环次数 < 100?}
                    N -->|是| O[📝 attempt += 1]
                    O --> G

                    %% 失败结束条件
                    N -->|否| P[⚠️ 达到最大尝试次数<br/>100次循环完成]

                    %% 两个结束点
                    M --> Q[📄 最终Excel报告<br/>pico_results.xlsx]
                    P --> Q

                    %% 样式设置
                    style A fill:#e1f5fe
                    style B fill:#f3e5f5
                    style C fill:#fff3e0
                    style D fill:#e8f5e8
                    style E fill:#fce4ec
                    style F fill:#fff8e1
                    style G fill:#ffecb3
                    style H fill:#e0f2f1
                    style I fill:#fff8e1
                    style J fill:#f1f8e9
                    style K fill:#e8eaf6
                    style L fill:#fff3e0
                    style M fill:#c8e6c9
                    style N fill:#fff3e0
                    style O fill:#e1f5fe
                    style P fill:#ffcdd2
                    style Q fill:#e8f5e8
            </div>
        </div>

        <div class="flowchart-container">
            <h3>🔄 检索式迭代优化详解</h3>
            <div class="mermaid">
                graph TD
                    A[循环开始<br/>attempt = 1-100] --> B[使用当前检索式<br/>PubMed搜索]
                    B --> C{搜索结果 > 0?}

                    %% 情况1: 搜索无结果
                    C -->|否| D1[🔄 触发检索式优化<br/>情况1: 搜索无结果]
                    D1 --> E1[Gemini.refine_pubmed_query<br/>扩大搜索范围]
                    E1 --> F1{attempt < 100?}
                    F1 -->|是| G1[更新current_query<br/>continue下一轮]
                    F1 -->|否| H1[⚠️ 达到上限退出]

                    %% 情况2: 有结果但无新PMID
                    C -->|是| I[获取PMID列表]
                    I --> J[过滤已处理PMID]
                    J --> K{有新PMID?}
                    K -->|否| D2[🔄 触发检索式优化<br/>情况2: 无新PMID]
                    D2 --> E2[Gemini.refine_pubmed_query<br/>寻找新的文献角度]
                    E2 --> F2{attempt < 100?}
                    F2 -->|是| G2[更新current_query<br/>continue下一轮]
                    F2 -->|否| H2[⚠️ 达到上限退出]

                    %% 情况3: 有新PMID，进行处理
                    K -->|是| L[获取前600篇详情]
                    L --> M[20线程并发筛选]
                    M --> N[累积结果到Excel]
                    N --> O{总文献数 ≥ 500?}

                    %% 成功结束
                    O -->|是| P[✅ 成功完成]

                    %% 情况3: 文献不足，需要更多
                    O -->|否| D3[🔄 触发检索式优化<br/>情况3: 文献数不足]
                    D3 --> E3[Gemini.refine_pubmed_query<br/>寻找更多相关文献]
                    E3 --> F3{attempt < 100?}
                    F3 -->|是| G3[更新current_query<br/>continue下一轮]
                    F3 -->|否| H3[⚠️ 达到上限退出]

                    %% 循环继续
                    G1 --> A
                    G2 --> A
                    G3 --> A

                    style A fill:#e1f5fe
                    style B fill:#fce4ec
                    style C fill:#fff3e0
                    style D1 fill:#ffecb3
                    style D2 fill:#ffecb3
                    style D3 fill:#ffecb3
                    style E1 fill:#f3e5f5
                    style E2 fill:#f3e5f5
                    style E3 fill:#f3e5f5
                    style F1 fill:#fff3e0
                    style F2 fill:#fff3e0
                    style F3 fill:#fff3e0
                    style G1 fill:#e1f5fe
                    style G2 fill:#e1f5fe
                    style G3 fill:#e1f5fe
                    style H1 fill:#ffcdd2
                    style H2 fill:#ffcdd2
                    style H3 fill:#ffcdd2
                    style I fill:#e0f2f1
                    style J fill:#f1f8e9
                    style K fill:#fff3e0
                    style L fill:#e0f2f1
                    style M fill:#fff8e1
                    style N fill:#e8eaf6
                    style O fill:#fff3e0
                    style P fill:#c8e6c9
            </div>
        </div>

        <div class="flowchart-container">
            <h3>🧠 Gemini检索式优化策略</h3>
            <div class="mermaid">
                graph TD
                    A[调用refine_pubmed_query] --> B{优化触发原因}

                    B -->|搜索无结果| C1[策略1: 扩大搜索范围<br/>- 添加同义词<br/>- 减少限制条件<br/>- 使用更宽泛的MeSH词]

                    B -->|无新PMID| C2[策略2: 改变搜索角度<br/>- 调整关键词组合<br/>- 尝试不同的字段搜索<br/>- 修改时间范围]

                    B -->|文献数不足| C3[策略3: 增强召回率<br/>- 放宽筛选条件<br/>- 增加相关主题<br/>- 扩展研究类型]

                    C1 --> D[Gemini AI分析<br/>当前检索式的问题]
                    C2 --> D
                    C3 --> D

                    D --> E[基于以下信息生成新检索式:<br/>- 用户原始查询<br/>- PICO标准<br/>- 当前检索式<br/>- 搜索结果数量<br/>- 当前轮次<br/>- 已有文献数量]

                    E --> F[输出优化后的检索式]
                    F --> G[返回main.py继续循环]

                    style A fill:#e1f5fe
                    style B fill:#fff3e0
                    style C1 fill:#ffecb3
                    style C2 fill:#e8f5e8
                    style C3 fill:#f3e5f5
                    style D fill:#fff8e1
                    style E fill:#e8eaf6
                    style F fill:#c8e6c9
                    style G fill:#e1f5fe
            </div>
        </div>

        <div class="flowchart-container">
            <h3>🛠️ 实际错误处理机制</h3>
            <div class="mermaid">
                graph TD
                    A[PubMed API请求] --> B{请求成功?}
                    B -->|成功| C[解析XML数据]
                    B -->|失败| D[Tenacity自动重试<br/>最多3次，指数退避]

                    D --> E{重试成功?}
                    E -->|是| C
                    E -->|否| F[记录错误日志<br/>继续主循环]

                    C --> G[Gemini AI筛选]
                    G --> H{AI响应成功?}
                    H -->|成功| I[解析JSON结果]
                    H -->|失败| J[Tenacity自动重试<br/>Gemini API]

                    J --> K{重试成功?}
                    K -->|是| I
                    K -->|否| L[跳过该文献<br/>继续处理下一篇]

                    I --> M{JSON解析成功?}
                    M -->|是| N[保存文献结果]
                    M -->|否| O[清理markdown标记<br/>重新解析]

                    O --> P{重新解析成功?}
                    P -->|是| N
                    P -->|否| L

                    F --> Q[主循环继续]
                    L --> Q
                    N --> Q

                    style A fill:#e1f5fe
                    style B fill:#fff3e0
                    style C fill:#e8f5e8
                    style D fill:#ffecb3
                    style E fill:#fff3e0
                    style F fill:#ffcdd2
                    style G fill:#f3e5f5
                    style H fill:#fff3e0
                    style I fill:#e8f5e8
                    style J fill:#ffecb3
                    style K fill:#fff3e0
                    style L fill:#ffcdd2
                    style M fill:#fff3e0
                    style N fill:#c8e6c9
                    style O fill:#ffecb3
                    style P fill:#fff3e0
                    style Q fill:#e1f5fe
            </div>
        </div>

        <div class="flowchart-container">
            <h3>🔧 技术架构图</h3>
            <div class="mermaid">
                graph TB
                    subgraph "🎯 用户层"
                        A[命令行界面<br/>python3 main.py]
                    end

                    subgraph "🤖 AI处理层"
                        B[Gemini 2.5-pro<br/>PICO生成]
                        C[Gemini 2.5-flash-lite<br/>查询生成+文献筛选]
                    end

                    subgraph "🌐 数据获取层"
                        D[PubMed E-utilities API<br/>esearch + efetch]
                        E[POST请求优化<br/>避免URL长度限制]
                    end

                    subgraph "⚙️ 处理层"
                        F[XML解析器<br/>提取文献信息]
                        G[ThreadPoolExecutor<br/>并发处理]
                        H[期刊数据库<br/>影响因子+分区]
                    end

                    subgraph "💾 输出层"
                        I[Excel生成器<br/>openpyxl]
                        J[缓存系统<br/>PMID去重]
                    end

                    A --> B
                    A --> C
                    C --> D
                    D --> E
                    E --> F
                    F --> G
                    G --> H
                    H --> I
                    I --> J

                    style A fill:#e1f5fe
                    style B fill:#f3e5f5
                    style C fill:#f3e5f5
                    style D fill:#fce4ec
                    style E fill:#fce4ec
                    style F fill:#fff8e1
                    style G fill:#fff8e1
                    style H fill:#fff8e1
                    style I fill:#e8f5e8
                    style J fill:#e8f5e8
            </div>
        </div>

        <div class="flowchart-container">
            <h3>🧵 实际并发处理机制</h3>
            <div class="mermaid">
                graph TD
                    A[每轮获取≤600篇新文献] --> B[创建ThreadPoolExecutor<br/>max_workers=20]
                    B --> C[为每篇文献创建任务<br/>包含文献+PICO+查询信息]

                    C --> D[20个线程并发执行<br/>screen_article函数]

                    D --> E1[线程1: 处理文献A<br/>调用Gemini AI]
                    D --> E2[线程2: 处理文献B<br/>调用Gemini AI]
                    D --> E3[线程3: 处理文献C<br/>调用Gemini AI]
                    D --> E4[... 最多20个线程<br/>同时运行]

                    E1 --> F1{文献A相关?}
                    E2 --> F2{文献B相关?}
                    E3 --> F3{文献C相关?}
                    E4 --> F4{其他文献相关?}

                    F1 -->|是| G1[提取详细信息<br/>返回结构化数据]
                    F1 -->|否| H1[返回None]
                    F2 -->|是| G2[提取详细信息<br/>返回结构化数据]
                    F2 -->|否| H2[返回None]
                    F3 -->|是| G3[提取详细信息<br/>返回结构化数据]
                    F3 -->|否| H3[返回None]
                    F4 -->|是| G4[提取详细信息<br/>返回结构化数据]
                    F4 -->|否| H4[返回None]

                    G1 --> I[收集所有非None结果]
                    G2 --> I
                    G3 --> I
                    G4 --> I
                    H1 --> I
                    H2 --> I
                    H3 --> I
                    H4 --> I

                    I --> J[累积到总结果列表<br/>更新Excel文件]
                    J --> K[更新PMID缓存<br/>避免重复处理]

                    style A fill:#e1f5fe
                    style B fill:#f3e5f5
                    style C fill:#fff3e0
                    style D fill:#e8f5e8
                    style E1 fill:#fff8e1
                    style E2 fill:#fff8e1
                    style E3 fill:#fff8e1
                    style E4 fill:#fff8e1
                    style F1 fill:#f3e5f5
                    style F2 fill:#f3e5f5
                    style F3 fill:#f3e5f5
                    style F4 fill:#f3e5f5
                    style G1 fill:#c8e6c9
                    style G2 fill:#c8e6c9
                    style G3 fill:#c8e6c9
                    style G4 fill:#c8e6c9
                    style H1 fill:#ffcdd2
                    style H2 fill:#ffcdd2
                    style H3 fill:#ffcdd2
                    style H4 fill:#ffcdd2
                    style I fill:#e8eaf6
                    style J fill:#e8f5e8
                    style K fill:#f1f8e9
            </div>
        </div>

        <h3>📝 详细步骤说明</h3>
        <div class="workflow-step">
            <span class="emoji">🤖</span><strong>AI分析问题</strong> - 理解您的研究问题，识别关键要素
        </div>
        <div class="workflow-step">
            <span class="emoji">📋</span><strong>生成PICO标准</strong> - 制定科学的文献筛选标准
        </div>
        <div class="workflow-step">
            <span class="emoji">🔍</span><strong>生成检索式</strong> - 创建专业的PubMed搜索语句
        </div>
        <div class="workflow-step">
            <span class="emoji">📚</span><strong>搜索文献</strong> - 在PubMed数据库中搜索相关文献
        </div>
        <div class="workflow-step">
            <span class="emoji">🎯</span><strong>智能筛选</strong> - 根据PICO标准筛选最相关的文献
        </div>
        <div class="workflow-step">
            <span class="emoji">📊</span><strong>数据整理</strong> - 提取关键信息并添加期刊数据
        </div>
        <div class="workflow-step">
            <span class="emoji">📋</span><strong>生成报告</strong> - 创建专业的Excel报告
        </div>

        <h2 id="installation">🚀 首次安装 (只需做一次，约5-10分钟)</h2>
        
        <div class="highlight">
            在开始使用前，我们需要为电脑进行一次简单的"装修"。
        </div>

        <h3>方法一：快速安装（推荐）</h3>
        <div class="success">
            <strong>一键安装：</strong> 使用我们提供的安装脚本，自动完成所有配置。
        </div>
        
        <div class="code-block">
# 1. 进入项目目录
cd 您的项目路径

# 2. 运行安装脚本
./setup.sh

# 3. 按照提示完成配置
        </div>

        <h3>方法二：手动安装步骤</h3>
        
        <h4>1. 安装Python (如果电脑里没有的话)</h4>
        <p>Python是本工具运行的基础。</p>
        <ul>
            <li><strong>如何检查？</strong> 打开终端（Mac）或命令提示符（Windows），输入 <code>python3 --version</code> 然后回车。如果显示出版本号（如 Python 3.9.6），说明已安装，可跳过此步。</li>
            <li><strong>如何安装？</strong> 访问官网 <a href="https://www.python.org/downloads/" target="_blank">https://www.python.org/downloads/</a>，下载并安装最新版本。安装时，请务必勾选 "Add Python to PATH" 或类似的选项。</li>
        </ul>

        <h4>2. 配置您的专属"通行证" (API密钥)</h4>
        <p>这个"通行证"是让工具能够使用强大AI模型的关键。</p>
        
        <div class="workflow-step">
            <strong>获取Google Gemini API密钥：</strong><br>
            1. 访问 <a href="https://aistudio.google.com/app/apikey" target="_blank">https://aistudio.google.com/app/apikey</a><br>
            2. 登录您的Google账号<br>
            3. 点击"Create API Key"创建新密钥<br>
            4. 复制生成的API密钥（以AIzaSy开头的长字符串）
        </div>
        
        <div class="workflow-step">
            <strong>配置密钥：</strong><br>
            - 在本项目文件夹里，找到一个名为 <code>.env</code> 的文件。如果找不到，可以复制一份 <code>.env.example</code> 文件并将其重命名为 <code>.env</code>。<br>
            - 用记事本或任何文本编辑器打开 <code>.env</code> 文件。<br>
            - 将文件中的 <code>your_key_here</code> 替换为您自己的Google Gemini API密钥。
        </div>
        
        <div class="code-block">
GEMINI_API_KEY=AIzaSy... (一长串字符)
        </div>

        <h4>3. 安装所有"零件" (依赖库)</h4>
        <div class="code-block">
# 进入项目目录
cd Desktop/pubmed_meta_search-袁泽

# 安装依赖
pip3 install -r 说明文档/requirements.txt
        </div>

        <h4>4. 设置文件权限（Mac/Linux用户）</h4>
        <div class="code-block">
chmod +x main.py
chmod +x setup.sh
        </div>

        <h2 id="usage">📖 开始使用 (每次都这样做)</h2>
        
        <div class="success">
            "装修"完成后，每次使用就非常简单了！
        </div>

        <h3>1. 打开终端并进入项目目录</h3>
        <p>像安装时一样，打开终端或命令提示符，并用 cd 命令进入本项目的文件夹。</p>

        <h3>2. 下达指令</h3>
        <div class="code-block">
# 指令格式
python3 main.py "您的研究问题"

# 示例1：眼科研究
python3 main.py "检索雷珠单抗治疗nAMD的所有临床研究"

# 示例2：肿瘤研究
python3 main.py "PD-1抑制剂治疗非小细胞肺癌的疗效和安全性"

# 示例3：心血管研究
python3 main.py "他汀类药物对冠心病患者预后的影响"
        </div>

        <h3>3. 查看结果</h3>
        <ul>
            <li>工具开始运行后，您会在终端窗口看到实时的工作日志。</li>
            <li>根据您的问题复杂度和网络情况，整个过程可能需要几分钟到几十分钟不等。</li>
            <li><strong>任务完成的标志:</strong> 当终端不再滚动日志，并回到您可以输入命令的状态时，就表示任务完成了。</li>
            <li><strong>最终成果:</strong> 在项目文件夹中，找到一个名为 <code>pico_results.xlsx</code> 的Excel文件。所有符合条件的文献，连同期刊信息和PubMed链接，都已为您整齐地排列在这份报告中。</li>
        </ul>

        <h2 id="models">🤖 AI模型配置</h2>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🎯 PICO标准生成</h4>
                <p><strong>模型:</strong> Gemini 2.5-pro<br>
                <strong>用途:</strong> 生成高质量的PICO标准<br>
                <strong>特点:</strong> 最高准确性，复杂推理能力强</p>
            </div>
            <div class="feature-card">
                <h4>🔍 检索式生成</h4>
                <p><strong>模型:</strong> Gemini 2.5-flash-lite<br>
                <strong>用途:</strong> 生成PubMed检索语句<br>
                <strong>特点:</strong> 快速响应，高质量输出</p>
            </div>
            <div class="feature-card">
                <h4>📚 文献筛选</h4>
                <p><strong>模型:</strong> Gemini 2.5-flash-lite<br>
                <strong>用途:</strong> 智能筛选相关文献<br>
                <strong>特点:</strong> 并发处理，精准筛选</p>
            </div>
        </div>

        <h2 id="troubleshooting">🛠️ 常见问题解答</h2>
        
        <div class="qa-item">
            <div class="qa-question">Q1: 为什么运行时提示"Permission denied"？</div>
            <div class="qa-answer">A: 这是文件权限问题。请运行：<code>chmod +x main.py</code> 或者直接运行 <code>./setup.sh</code></div>
        </div>
        
        <div class="qa-item">
            <div class="qa-question">Q2: 为什么提示找不到某些Python包？</div>
            <div class="qa-answer">A: 请确保已安装所有依赖：<code>pip3 install -r 说明文档/requirements.txt</code></div>
        </div>
        
        <div class="qa-item">
            <div class="qa-question">Q3: 可以同时运行多个查询吗？</div>
            <div class="qa-answer">A: 建议一次只运行一个查询，避免API调用冲突。</div>
        </div>
        
        <div class="qa-item">
            <div class="qa-question">Q4: 生成的Excel文件在哪里？</div>
            <div class="qa-answer">A: 在项目根目录下，文件名为 <code>pico_results.xlsx</code></div>
        </div>
        
        <div class="qa-item">
            <div class="qa-question">Q5: 如何获得更好的检索结果？</div>
            <div class="qa-answer">A: 使用具体、明确的研究问题；包含关键的医学术语；避免过于宽泛的描述</div>
        </div>
        
        <div class="qa-item">
            <div class="qa-question">Q6: 工具支持哪些语言？</div>
            <div class="qa-answer">A: 支持中文和英文研究问题，输出结果为中文。</div>
        </div>

        <div class="qa-item">
            <div class="qa-question">Q7: 为什么设置为500篇文献和100次循环？</div>
            <div class="qa-answer">A: 这是经过优化的性能参数，既保证了效率，又确保了结果质量。500篇高质量文献对于大多数系统综述研究来说是一个很好的规模，100次循环确保有足够的搜索尝试。</div>
        </div>

        <div class="qa-item">
            <div class="qa-question">Q8: Excel报告中的PMC链接是什么？</div>
            <div class="qa-answer">A: PMC链接是PubMed Central的全文链接，点击可直接访问文献的免费全文（如果可用）。系统会自动提取PMCID并生成相应的PMC链接。</div>
        </div>

        <h2 id="support">📞 技术支持</h2>
        
        <div class="success">
            <h4>如果遇到技术问题，请：</h4>
            <ul>
                <li>检查网络连接是否正常</li>
                <li>确认API密钥是否正确配置</li>
                <li>查看终端输出的错误信息</li>
                <li>参考技术文档 <code>TECHNICAL_GUIDE.md</code> 获取更详细的信息</li>
                <li>查看运行日志 <code>app.log</code> 文件</li>
                <li>运行 <code>./setup.sh</code> 进行快速诊断</li>
            </ul>
        </div>

        <h3>版本信息</h3>
        <ul>
            <li><strong>当前版本</strong>: v2.1.0</li>
            <li><strong>Python要求</strong>: 3.9+</li>
            <li><strong>主要依赖</strong>: requests, pandas, openpyxl, google-generativeai</li>
            <li><strong>性能参数</strong>: 最多100次循环，目标500篇文献，20线程并发</li>
            <li><strong>新增功能</strong>: PMCID提取，PMC链接生成</li>
        </ul>

        <div class="highlight">
            <strong>🎉 祝您使用愉快！</strong> 如有任何问题，请参考相关文档或查看应用日志获取详细信息。
        </div>
    </div>
</body>
</html>
