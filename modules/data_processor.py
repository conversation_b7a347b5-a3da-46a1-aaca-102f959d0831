import logging
import os
import json
import xmltodict
import pandas as pd
import csv
from typing import List, Dict, Any, Optional
from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON>Fill, Font, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

# Configure the logger for this module
logger = logging.getLogger(__name__)

def parse_pubmed_xml(xml_data: str) -> List[Dict[str, Any]]:
    """
    Parses raw XML data from PubMed into a list of article dictionaries.
    """
    try:
        logger.info("Parsing PubMed XML data...")
        data_dict = xmltodict.parse(xml_data)
        articles = data_dict.get("PubmedArticleSet", {}).get("PubmedArticle", [])
        if isinstance(articles, dict):
            articles = [articles]
        logger.info(f"Successfully parsed {len(articles)} articles from XML.")
        return articles
    except Exception as e:
        logger.error(f"Failed to parse PubMed XML: {e}", exc_info=True)
        return []

def _safe_get(data: Dict, path: List[str], default: Any = None) -> Any:
    """Safely get a value from a nested dictionary."""
    for key in path:
        if not isinstance(data, dict) or key not in data:
            return default
        data = data[key]
    return data

def extract_article_metadata(article_dict: Dict[str, Any]) -> Dict[str, Optional[str]]:
    """
    Extracts key metadata from a single article dictionary with robust title extraction.
    """
    pmid = _safe_get(article_dict, ["MedlineCitation", "PMID", "#text"])

    # --- PMCID Extraction ---
    pmcid = None
    article_id_list = _safe_get(article_dict, ["PubmedData", "ArticleIdList", "ArticleId"])
    if article_id_list:
        if isinstance(article_id_list, list):
            for article_id in article_id_list:
                if isinstance(article_id, dict) and article_id.get("@IdType") == "pmc":
                    pmcid = article_id.get("#text")
                    break
        elif isinstance(article_id_list, dict) and article_id_list.get("@IdType") == "pmc":
            pmcid = article_id_list.get("#text")

    # --- Robust Title Extraction ---
    title_node = _safe_get(article_dict, ["MedlineCitation", "Article", "ArticleTitle"])
    title = None
    if isinstance(title_node, dict):
        title = title_node.get("#text")
    elif isinstance(title_node, str):
        title = title_node
    
    journal = _safe_get(article_dict, ["MedlineCitation", "Article", "Journal", "Title"])
    issn = _safe_get(article_dict, ["MedlineCitation", "Article", "Journal", "ISSN", "#text"])
    authors_data = _safe_get(article_dict, ["MedlineCitation", "Article", "AuthorList", "Author"])
    first_author = None
    corresponding_author = None
    first_affiliation = None

    if authors_data:
        if isinstance(authors_data, list) and len(authors_data) > 0:
            first_author_data = authors_data[0]
            if first_author_data:
                fname = _safe_get(first_author_data, ["ForeName"]) or ""
                lname = _safe_get(first_author_data, ["LastName"]) or ""
                first_author = f"{fname} {lname}".strip() or None
                affiliation_data = _safe_get(first_author_data, ["AffiliationInfo"])
                if affiliation_data:
                    if isinstance(affiliation_data, list) and len(affiliation_data) > 0:
                        first_affiliation = _safe_get(affiliation_data[0], ["Affiliation"])
                    else:
                        first_affiliation = _safe_get(affiliation_data, ["Affiliation"])
            last_author_data = authors_data[-1]
            if last_author_data:
                if _safe_get(last_author_data, ["CollectiveName"]):
                    corresponding_author = _safe_get(last_author_data, ["CollectiveName"])
                else:
                    fname = _safe_get(last_author_data, ["ForeName"]) or ""
                    lname = _safe_get(last_author_data, ["LastName"]) or ""
                    corresponding_author = f"{fname} {lname}".strip() or None

    abstract_parts = []
    abstract_node = _safe_get(article_dict, ["MedlineCitation", "Article", "Abstract", "AbstractText"])
    if abstract_node:
        if isinstance(abstract_node, list):
            for part in abstract_node:
                label = part.get('@Label', '')
                text = part.get('#text', '')
                abstract_parts.append(f"[{label}] {text}" if label else text)
        elif isinstance(abstract_node, dict):
            label = abstract_node.get('@Label', '')
            text = abstract_node.get('#text', '')
            abstract_parts.append(f"[{label}] {text}" if label else text)
        else:
            abstract_parts.append(abstract_node)
    abstract = "\\n\\n".join(abstract_parts)

    pub_date_node = _safe_get(article_dict, ["MedlineCitation", "Article", "Journal", "JournalIssue", "PubDate"])
    year = _safe_get(pub_date_node, ["Year"])
    month = _safe_get(pub_date_node, ["Month"])
    day = _safe_get(pub_date_node, ["Day"])
    formatted_date = None
    if year:
        if month and day:
            month_map = {'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'}
            month_num = month_map.get(month, month)
            if len(str(month_num)) == 1: month_num = '0' + str(month_num)
            if len(str(day)) == 1: day = '0' + str(day)
            formatted_date = f"{year}-{month_num}-{day}"
        elif month:
            month_map = {'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'}
            month_num = month_map.get(month, month)
            if len(str(month_num)) == 1: month_num = '0' + str(month_num)
            formatted_date = f"{year}-{month_num}"
        else:
            formatted_date = str(year)

    return {
        "pmid": pmid, "pmcid": pmcid, "title": title, "journal": journal, "issn": issn,
        "first_author": first_author, "corresponding_author": corresponding_author,
        "first_affiliation": first_affiliation, "abstract": abstract or "No abstract available.",
        "publication_date": formatted_date,
    }

def _load_journal_ref_to_dict(reference_file: str) -> Dict[str, Dict[str, str]]:
    """Loads the journal reference CSV into a dictionary keyed by normalized ISSN."""
    journal_dict = {}
    try:
        # 尝试UTF-8编码，如果失败则尝试GBK编码
        try:
            with open(reference_file, 'r', encoding='utf-8') as f:
                content = f.read()
            encoding = 'utf-8'
        except UnicodeDecodeError:
            with open(reference_file, 'r', encoding='gbk') as f:
                content = f.read()
            encoding = 'gbk'

        # 使用检测到的编码重新打开文件
        with open(reference_file, 'r', encoding=encoding) as f:
            reader = csv.reader(f)
            next(reader)  # Skip header
            for row in reader:
                if len(row) < 11: continue

                # 新文件列名映射：Journal、缩写、ISSN、EISSN、Catalog、Publisher、CAS-zone、JCR-zone、2024-IF、5-year-IF、JIF Rank
                journal_info = {
                    '期刊名称_ref': row[0].strip(),      # Journal
                    '大类分区': row[6].strip(),          # CAS-zone (中科院分区)
                    'JCR分区': row[7].strip(),           # JCR-zone
                    '最新IF': row[8].strip(),            # 2024-IF
                    '5年IF': row[9].strip(),             # 5-year-IF
                    '排名': row[10].strip()              # JIF Rank
                }

                print_issn = row[2].strip().replace('-', '')  # ISSN
                e_issn = row[3].strip().replace('-', '')      # EISSN

                if print_issn:
                    journal_dict[print_issn] = journal_info
                if e_issn:
                    journal_dict[e_issn] = journal_info
        logger.info(f"Successfully loaded {len(journal_dict)} journal entries into dictionary.")
    except Exception as e:
        logger.error(f"Failed to load reference file into dictionary: {e}", exc_info=True)
    return journal_dict

def generate_formatted_excel(screened_articles: List[Dict[str, Any]], reference_file: str, output_path: str):
    """
    Generates a fully formatted Excel file, enriching articles with journal data.
    """
    if not screened_articles:
        logger.warning("No articles to process. Excel file will not be generated.")
        return

    journal_ref_dict = _load_journal_ref_to_dict(reference_file)

    enriched_articles = []
    for article in screened_articles:
        enriched_article = article.copy()
        issn = article.get("ISSN", "")
        normalized_issn = issn.replace('-', '').strip() if issn else ""
        
        journal_info = journal_ref_dict.get(normalized_issn, {})
        
        enriched_article['期刊名称'] = journal_info.get('期刊名称_ref', article.get('期刊名称'))
        enriched_article['大类分区'] = journal_info.get('大类分区', '')
        enriched_article['JCR分区'] = journal_info.get('JCR分区', '')
        enriched_article['最新IF'] = journal_info.get('最新IF', '')
        enriched_article['5年IF'] = journal_info.get('5年IF', '')
        enriched_article['排名'] = journal_info.get('排名', '')
        
        # Add PubMed Link
        pmid = enriched_article.get('PMID')
        if pmid:
            enriched_article['PubMed链接'] = f"https://pubmed.ncbi.nlm.nih.gov/{pmid}/"
        else:
            enriched_article['PubMed链接'] = ""

        # Add PMC Link
        pmcid = enriched_article.get('PMCID')
        if pmcid:
            enriched_article['PMC链接'] = f"https://pmc.ncbi.nlm.nih.gov/articles/{pmcid}/"
        else:
            enriched_article['PMC链接'] = ""

        enriched_articles.append(enriched_article)

    final_df = pd.DataFrame(enriched_articles)
    
    final_columns = [
        "序号", "期刊名称", "ISSN", "大类分区", "JCR分区", "最新IF", "5年IF", "排名",
        "PMID", "文章标题", "第一作者", "通讯作者", "第一作者单位", "发表时间",
        "研究目的", "研究类型", "研究方法", "研究对象", "主要研究结果", "研究结论与意义", "研究亮点或创新点",
        "PubMed链接", "PMC链接"
    ]
    final_df = final_df.reindex(columns=final_columns)
    final_df['序号'] = range(1, len(final_df) + 1)

    try:
        logger.info(f"Writing formatted data to Excel file: {output_path}")
        wb = Workbook()
        ws = wb.active
        ws.title = "Screened Articles"

        green_fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
        thin_border = Border(left=Side(style='thin'), 
                             right=Side(style='thin'), 
                             top=Side(style='thin'), 
                             bottom=Side(style='thin'))
        cell_alignment = Alignment(wrap_text=True, vertical="center", horizontal="left")
        hyperlink_font = Font(color="0563C1", underline="single")

        for r_idx, row in enumerate(dataframe_to_rows(final_df, index=False, header=True), 1):
            ws.append(row)
            for cell in ws[r_idx]:
                cell.alignment = cell_alignment
                cell.border = thin_border
            
            if r_idx % 2 != 0: # Apply alternating row color to ODD rows (1, 3, 5...)
                for cell in ws[r_idx]:
                    cell.fill = green_fill
            
            # Add hyperlink
            if r_idx > 1:
                link_cell = ws.cell(row=r_idx, column=len(final_columns))
                if link_cell.value:
                    link_cell.hyperlink = link_cell.value
                    link_cell.font = hyperlink_font

        # Set column widths
        column_widths = {
            'A': 5, 'B': 40, 'C': 12, 'D': 8, 'E': 8, 'F': 8, 'G': 8, 'H': 10,
            'I': 10, 'J': 60, 'K': 15, 'L': 15, 'M': 30, 'N': 12, 'O': 50,
            'P': 20, 'Q': 50, 'R': 30, 'S': 50, 'T': 50, 'U': 50, 'V': 30
        }
        for col, width in column_widths.items():
            ws.column_dimensions[col].width = width
        
        ws.freeze_panes = 'A2'
        ws.auto_filter.ref = ws.dimensions

        wb.save(output_path)
        logger.info(f"Successfully saved {len(final_df)} articles to {output_path}")

    except Exception as e:
        logger.error(f"Failed to write formatted Excel file: {e}", exc_info=True)