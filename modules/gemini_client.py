import logging
import os
import google.generativeai as genai
from tenacity import retry, stop_after_attempt, wait_exponential
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# Configure the logger for this module
logger = logging.getLogger(__name__)

class GeminiClient:
    """
    A client to handle all interactions with the Google Gemini API.

    This class encapsulates the logic for generating PICO standards, creating
    and refining PubMed search queries, and extracting structured information
    from article abstracts based on the PICO criteria.
    """
    def __init__(self):
        """
        Initializes the Gemini client.
        
        It configures the API key from environment variables and sets up the
        generative model. It will raise a ValueError if the API key is not set.
        """
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            msg = "GOOGLE_API_KEY environment variable not set. Please create a .env file and add it."
            logger.error(msg)
            raise ValueError(msg)
        
        genai.configure(api_key=api_key)
        # Using gemini-2.5-pro for PICO generation (highest accuracy)
        self.model_pro_25 = genai.GenerativeModel('gemini-2.5-pro')
        # Using gemini-2.5-flash-lite for query generation and article screening (faster and more accurate)
        self.model_flash_lite = genai.GenerativeModel('gemini-2.5-flash-lite')
        logger.info("Google Gemini client initialized successfully with gemini-2.5-flash-lite for query and screening tasks.")

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _generate_content(self, prompt: str, use_pro_model: bool = False) -> str:
        """
        A robust, private method to send a prompt to the Gemini API and get a response.
        
        Includes retry logic with exponential backoff to handle transient API errors.

        Args:
            prompt: The complete prompt string to send to the model.
            use_pro_model: Whether to use 2.5-pro (True) or 2.5-flash-lite (False).

        Returns:
            The text content of the model's response.

        Raises:
            Exception: If the API call fails after all retry attempts.
        """
        try:
            logger.info("Sending prompt to Gemini API...")
            safety_settings = {
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
            # Choose model: 2.5-pro for PICO generation only, 2.5-flash-lite for all other tasks
            model = self.model_pro_25 if use_pro_model else self.model_flash_lite
            response = model.generate_content(prompt, safety_settings=safety_settings)
            # Accessing the text part of the response safely
            if response.parts:
                response_text = response.text
                logger.info("Successfully received response from Gemini API.")
                return response_text
            else:
                # Handle cases where the response might be empty or blocked
                logger.warning("Received an empty or blocked response from Gemini API.")
                # You might want to inspect response.prompt_feedback here
                return ""
        except Exception as e:
            logger.error(f"An error occurred while communicating with the Gemini API: {e}", exc_info=True)
            raise

    def generate_pico_standard(self, user_query: str) -> str:
        """
        Generates a structured PICO standard from a user's natural language query.

        Args:
            user_query: The user's research topic description.

        Returns:
            A string containing the structured PICO standard.
        """
        # This prompt is a simplified version of the one in the n8n workflow.
        # It's engineered to be direct and effective for the Python script context.
        prompt = f"""
        As an expert in clinical research, create a precise and focused PICO standard based on the following user query.
        The PICO standard should be clear and serve as inclusion criteria for a meta-analysis.

        IMPORTANT GUIDELINES:
        1. Use the EXACT drug names, medical terms, and conditions mentioned in the user query.
        2. Do NOT translate or substitute drug names - preserve the original terminology precisely.
        3. For Chinese drug names, provide the accurate English equivalent if known.
        4. **For Outcome (O): Only include outcomes that are DIRECTLY related to what the user is asking about.**
        5. **Do NOT create an exhaustive list of all possible outcomes - focus on the specific outcomes implied by the user's question.**
        6. **If the user asks about "疗效" (efficacy/effectiveness), focus on the main therapeutic outcomes, not safety outcomes.**

        Format:
        PICO标准:
        ----------------------------------------
        P (Population/Problem):
        - [Specific patient population mentioned in the query]

        I (Intervention):
        - [Specific intervention/treatment mentioned in the query]

        C (Comparison):
        - [Appropriate comparison group - can be placebo, standard care, or other treatments]

        O (Outcome):
        - [Only the specific outcomes that directly answer the user's research question]

        User Query: "{user_query}"

        Output only the PICO standard in the exact format shown above, with no additional text or explanation.
        """
        logger.info(f"Generating PICO standard for query: '{user_query}'")
        return self._generate_content(prompt, use_pro_model=True)

    def generate_pubmed_query(self, user_query: str, pico_standard: str, attempt_number: int = 1) -> str:
        """
        Generates a PubMed search query based on the user's query using comprehensive search instructions.

        Args:
            user_query: The user's original research topic.
            pico_standard: The complete PICO standard document content (for reference only).

        Returns:
            A PubMed-compliant search string.
        """
        # 前3轮：基于用户问题的宽松检索，不考虑PICO
        if attempt_number <= 3:
            prompt = f"""
            **避免在开头出现"```pubmed"诸如此类的废话！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！直接给我我可以直接复制到pubmed检索框上进行检索的检索式就好而无需其他任何废话！！**

            **第{attempt_number}轮检索 - 宽松策略：基于用户问题"{user_query}"生成宽泛的检索式**

            根据用户的中文或英文口头描述，自动创建**宽泛、包容性强**的PubMed检索式。**忽略PICO标准限制，专注于用户问题的核心主题**。

            **宽松检索原则：**
            - 使用更多的同义词和相关术语
            - 大量使用OR连接符扩大检索范围
            - 使用截词符*扩展词干
            - 包含相关的研究类型和方法学术语
            - 不限制人群、干预或结局的具体类型

            **只输出pubmed的标准检索式，而不需要其他任何文字说明**

            规则：
            - 无论用户使用的是中文还是英文描述，生成的检索式必须为英文
            - 对于需要精确匹配的多词短语，应使用双引号包围，如"myocardial infarction"[Title/Abstract]
            - 每个独立的检索单元必须使用括号包裹，例如("lung cancer"[Title/Abstract])
            - 关键词检索默认使用[Title/Abstract]字段，除非用户明确指定其他字段
            - 大量使用OR连接相关术语，适度使用AND连接主要概念
            - 使用截词符号*进行词干扩展，例如(diagnos*[Title/Abstract])
            - 必要时使用医学主题词(MeSH Terms)进行检索，例如(prognosis[MeSH Terms])

            **PubMed标准字段标签（只能使用以下字段）：**
            - [Title/Abstract] 或 [tiab] - 标题和摘要
            - [MeSH Terms] 或 [mesh] - 医学主题词
            - [Title] 或 [ti] - 仅标题
            - [Abstract] 或 [ab] - 仅摘要
            - [Author] 或 [au] - 作者
            - [Journal] 或 [ta] - 期刊名
            - [Publication Type] 或 [pt] - 发表类型
            - [All Fields] 或 [all] - 所有字段
            - [Text Word] 或 [tw] - 文本词

            **严禁使用的错误字段标签：**
            - [Supplementary Concept] ❌
            - [Chemical] ❌
            - [Keyword] ❌
            - 任何其他非标准字段标签 ❌

            **绝对禁止：**
            - 严禁在生成的PubMed检索式中包含任何与JCR分区、中科院分区、影响因子(IF)、引用次数、H-index或任何其他期刊评价指标相关的信息
            - 如果用户的请求中提到了这些指标，必须完全忽略这些部分
            - 生成的检索式只能包含PubMed支持的标准字段

            用户查询: "{user_query}"

            直接输出宽泛的检索式：
            """
        else:
            # 第4轮开始：需要传递更多参数，这里先保持原有逻辑
            prompt = f"""
            **避免在开头出现"```pubmed"诸如此类的废话！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！直接给我我可以直接复制到pubmed检索框上进行检索的检索式就好而无需其他任何废话！！**

            根据用户的中文或英文口头描述"{user_query}"，自动创建准确、完整、标准化、英文且所有检索单元需加括号的PubMed检索式。**不要理会我是否添加了JCR、中科院分区或者影响因子等限定词，只需要给我完整详细具体准确的不添加分区或者影响因子的pubmed检索式就好**

            **只输出pubmed的标准检索式，而不需要其他任何文字说明**

            规则：
            - 无论用户使用的是中文还是英文描述，生成的检索式必须为英文
            - 对于需要精确匹配的多词短语，应使用双引号包围，如"myocardial infarction"[Title/Abstract]
            - 每个独立的检索单元必须使用括号包裹，例如("lung cancer"[Title/Abstract])
            - 关键词检索默认使用[Title/Abstract]字段，除非用户明确指定其他字段
            - 使用布尔逻辑符：AND、OR、NOT连接关键词，每个逻辑运算单元都必须使用括号包裹
            - 使用截词符号*进行词干扩展，例如(diagnos*[Title/Abstract])
            - 必要时使用医学主题词(MeSH Terms)进行检索，例如(prognosis[MeSH Terms])
            - 用户明确要求排除的内容，使用NOT明确排除，并且排除内容也必须用括号

            **PubMed标准字段标签（只能使用以下字段）：**
            - [Title/Abstract] 或 [tiab] - 标题和摘要
            - [MeSH Terms] 或 [mesh] - 医学主题词
            - [Title] 或 [ti] - 仅标题
            - [Abstract] 或 [ab] - 仅摘要
            - [Author] 或 [au] - 作者
            - [Journal] 或 [ta] - 期刊名
            - [Publication Type] 或 [pt] - 发表类型
            - [All Fields] 或 [all] - 所有字段
            - [Text Word] 或 [tw] - 文本词

            **严禁使用的错误字段标签：**
            - [Supplementary Concept] ❌
            - [Chemical] ❌
            - [Keyword] ❌
            - 任何其他非标准字段标签 ❌

            **绝对禁止：**
            - 严禁在生成的PubMed检索式中包含任何与JCR分区、中科院分区、影响因子(IF)、引用次数、H-index或任何其他期刊评价指标相关的信息
            - 如果用户的请求中提到了这些指标，必须完全忽略这些部分
            - 生成的检索式只能包含PubMed支持的标准字段

            用户查询: "{user_query}"

            直接输出检索式：
            """

        logger.info("Generating initial PubMed query using comprehensive search instructions.")
        return self._generate_content(prompt, use_pro_model=False)  # Use 2.5-flash-lite for query generation

    def refine_pubmed_query(self, user_query: str, pico_standard: str, failed_query: str, previous_count: int = 0, attempt_number: int = 1, current_results_count: int = 0) -> str:
        """
        Generates a new, refined PubMed query based on previous results.

        Args:
            user_query: The user's original research topic.
            pico_standard: The complete PICO standard document content.
            failed_query: The previous search query.
            previous_count: Number of articles found in the previous search.

        Returns:
            A new, refined PubMed-compliant search string.
        """
        # 前3轮：严格基于PICO的检索优化
        if attempt_number <= 3:
            strategy_instruction = f"""
            **第{attempt_number}轮检索优化 - 严格PICO策略**

            基于PICO标准严格优化检索式。上次检索找到了{previous_count}篇文献。

            **严格PICO优化原则：**
            - 严格按照PICO标准的P、I、C、O四个维度优化检索式
            - 确保检索式精确匹配PICO标准中定义的人群、干预、对照和结局
            - 使用PICO标准中的具体术语和概念
            - 避免偏离PICO标准范围的术语
            - 优先使用与PICO标准高度相关的MeSH术语
            """
        else:
            # 第4轮开始：根据上次搜索结果数量自适应调整查询复杂度
            if previous_count == 0:
                # 上次无结果，需要大幅简化查询
                strategy_instruction = f"""
                **第{attempt_number}轮检索优化 - 极度简化策略（上次0篇，当前已有{current_results_count}篇）**

                上次检索无结果，需要大幅简化查询，回到最基本的概念：

                **极度简化策略：**
                - **重新构建检索式**，不要在原查询基础上修改
                - 回到用户问题的核心概念，使用最基本的关键词
                - 大量使用OR连接同义词和相关术语
                - 使用截词符*最大化扩展
                - 避免复杂的AND组合
                - 包含广泛的研究类型（观察性研究、病例报告等）
                - 使用更通用的医学术语而非过于专业的词汇
                - 不限制特定人群或干预类型
                """
            elif previous_count < 50:
                # 上次结果较少，需要适度简化查询
                strategy_instruction = f"""
                **第{attempt_number}轮检索优化 - 简化策略（上次{previous_count}篇，当前已有{current_results_count}篇）**

                上次检索结果较少，需要适度简化查询以获得更多结果：

                **简化策略：**
                - 在核心概念基础上增加更多同义词
                - 增加OR连接符的使用，减少AND限制
                - 适度使用截词符扩展词干
                - 移除一些过于限制性的条件
                - 包含相关的研究方法和类型
                - 适当放宽人群或干预的限制
                """
            elif previous_count <= 400:
                # 上次结果适中，保持复杂度并优化检索词
                strategy_instruction = f"""
                **第{attempt_number}轮检索优化 - 平行策略（上次{previous_count}篇，当前已有{current_results_count}篇）**

                上次检索结果适中，保持当前复杂度并尝试不同的检索词组合：

                **平行策略：**
                - 保持当前检索式的复杂度水平
                - 使用不同的同义词和术语组合
                - 尝试不同的MeSH术语组合
                - 调整检索字段的组合方式
                - 平衡AND和OR的使用
                - 探索不同的概念角度
                """
            else:
                # 上次结果过多，需要增加查询复杂度
                strategy_instruction = f"""
                **第{attempt_number}轮检索优化 - 复杂化策略（上次{previous_count}篇，当前已有{current_results_count}篇）**

                上次检索结果过多，需要增加查询复杂度和精确度：

                **复杂化策略：**
                - 增加更多的AND连接条件
                - 使用更精确的医学术语和MeSH词汇
                - 添加研究类型限制（如仅RCT）
                - 增加人群特征的限制条件
                - 减少OR连接和截词符的使用
                - 提高检索的专业性和特异性
                - 严格按照PICO标准限制检索范围
                """

        prompt = f"""
        **避免在开头出现"```pubmed"诸如此类的废话！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！直接给我我可以直接复制到pubmed检索框上进行检索的检索式就好而无需其他任何废话！！**

        {strategy_instruction}

        通用要求：
        - 每个检索单元必须使用括号包裹
        - 严禁包含任何期刊评价指标（IF、JCR、中科院分区等）
        - 生成的检索式必须为英文
        - 使用标准的PubMed字段标签

        **PubMed标准字段标签（只能使用以下字段）：**
        - [Title/Abstract] 或 [tiab] - 标题和摘要
        - [MeSH Terms] 或 [mesh] - 医学主题词
        - [Title] 或 [ti] - 仅标题
        - [Abstract] 或 [ab] - 仅摘要
        - [Author] 或 [au] - 作者
        - [Journal] 或 [ta] - 期刊名
        - [Publication Type] 或 [pt] - 发表类型
        - [All Fields] 或 [all] - 所有字段
        - [Substance Name] - 物质名称
        - [Text Word] 或 [tw] - 文本词

        **严禁使用的错误字段标签：**
        - [Supplementary Concept] ❌
        - [Chemical] ❌
        - [Keyword] ❌
        - 任何其他非标准字段标签 ❌

        **只输出新的检索式，不需要其他任何文字说明**

        用户查询: "{user_query}"
        上次检索式: "{failed_query}"
        上次检索结果数量: {previous_count}篇

        直接输出新检索式：
        """

        logger.warning(f"Refining failed PubMed query: {failed_query}")
        return self._generate_content(prompt, use_pro_model=False)  # Use 2.5-flash-lite for query refinement

    def refine_pico_standard(self, research_question: str, pico_standard: str, num_included: int, max_study_threshold: int) -> str:
        """
        Calls the LLM to refine the PICO criteria to be more stringent based on the complete PICO document.
        """
        prompt = f"""You are an expert in systematic reviews. The following complete PICO document for the research question '{research_question}' resulted in {num_included} studies, which is more than our target of {max_study_threshold}. Your task is to rewrite the PICO criteria to be **more stringent and specific** to reduce the number of included studies.

        Please consider ALL information in the complete PICO document including user query context, generation time, and current PICO criteria. Retain the core research question but narrow the scope. Consider making one or more of the following changes:
            *   **Population:** Add stricter demographic limits (e.g., a narrower age range, specific disease stage, no comorbidities).
            *   **Intervention:** Be more specific about the treatment (e.g., specify dosage, duration, or administration method).
            *   **Comparison:** Restrict the acceptable control groups (e.g., require placebo-controlled only, exclude 'no treatment' comparators).
            *   **Outcome:** Focus on a more specific primary outcome or require a minimum follow-up period.
            *   **Study Design:** Require a more rigorous study design (e.g., randomized controlled trials only).

        Your output must be only the new, revised PICO criteria in the same format as the original PICO section.

            Complete Original PICO Document:
            {pico_standard}
            """
        logger.info("Refining PICO standard based on complete document...")
        return self._generate_content(prompt, use_pro_model=True)

    def extract_article_info(self, abstract: str, pico_standard: str, user_query: str = "", current_results_count: int = 0, attempt_number: int = 1) -> str:  ## **strictly **completely
        """
        Screens an article's abstract against the complete PICO standard document and extracts key information
        into a structured JSON format if it matches.

        Args:
            abstract: The abstract of the article.
            pico_standard: The complete PICO standard document content.
            user_query: The user's original research query for additional context.
            current_results_count: Number of articles already in pico_results.csv.

        Returns:
            A string containing the structured data in JSON format if the article is a match.
            Returns an empty JSON object string '{}' if it does not match.
        """
        # 前3轮：PICO筛选
        if attempt_number <= 3:
            strictness_instruction = f"""
            **第{attempt_number}轮筛选策略：PICO标准（固定策略）**

            **PICO符合性判断标准（需要全部满足）：**
            1. **Population (人群)**: 文献研究的人群需要与PICO标准中定义的人群一致或重叠
            2. **Intervention (干预)**: 文献研究的干预措施需要与PICO标准中定义的干预措施匹配
            3. **Comparison (对照)**: 文献研究的对照组需要与PICO标准中定义的对照组类型一致
            4. **Outcome (结局)**: 文献研究的主要结局指标需要与PICO标准中定义的结局指标直接相关

            **标准筛选原则：**
            - 如果文献在任何一个PICO维度上不符合标准，则输出 {{}}
            - 只有在各个PICO维度都明确符合的情况下，才能提取信息
            """
        else:
            # 第4轮开始：根据已有文献数量动态调整严格程度（始终保持PICO完整性）
            if current_results_count < 250:
                strictness_instruction = f"""
                **自适应筛选策略：（第{attempt_number}轮，已筛选{current_results_count}篇文献）**

                **PICO符合性判断标准（需要全部满足）：**
                1. **Population (人群)**: 文献研究的人群需要与PICO标准中定义的人群一致或重叠
                2. **Intervention (干预)**: 文献研究的干预措施需要与PICO标准中定义的干预措施匹配
                3. **Comparison (对照)**: 文献研究的对照组需要与PICO标准中定义的对照组类型一致
                4. **Outcome (结局)**: 文献研究的主要结局指标需要与PICO标准中定义的结局指标直接相关

                **标准筛选原则：**
                - 如果文献在任何一个PICO维度上不符合标准，则输出 {{}}
                - 只有在所有PICO维度都明确符合的情况下，才能提取信息
                """
            else:
                strictness_instruction = f"""
                **自适应筛选策略：极其严格（第{attempt_number}轮，已筛选{current_results_count}篇文献）**

                **PICO符合性判断标准（必须完美匹配）：**
                1. **Population (人群)**: 文献研究的人群必须与PICO标准中定义的人群完全一致，不允许任何偏差
                2. **Intervention (干预)**: 文献研究的干预措施必须与PICO标准中定义的干预措施完全匹配，包括剂量、方法等细节
                3. **Comparison (对照)**: 文献研究的对照组必须与PICO标准中定义的对照组类型完全一致
                4. **Outcome (结局)**: 文献研究的主要结局指标必须与PICO标准中定义的结局指标完全匹配

                **极严格筛选原则：**
                - 如果文献在任何一个PICO维度上有任何不完全匹配，必须输出 {{}}
                - 只接受在所有PICO维度都完美符合的高质量文献
                - 对于任何模糊、边缘或不确定情况，一律输出 {{}}
                - 严禁任何形式的宽泛解释或标准放宽
                """

        # 构建用户查询上下文
        user_query_section = ""
        if user_query.strip():
            user_query_section = f"""
        **用户原始查询**: "{user_query}"
        **重要提醒**: 除了PICO标准外，更需要关注用户原始查询中提到的具体要求、限制条件或关注点。
        """

        prompt = f"""
        输出格式：直接输出JSON，从{{开始到}}结束，不要任何其他文字。

        任务：判断文献是否符合PICO标准或者用户原始查询要求，符合则提取信息。
        {user_query_section}
        {strictness_instruction}

        PICO标准: {pico_standard}

        文献摘要: "{abstract}"

        判断流程：
        1. 检查P、I、C、O四个维度是否符合PICO标准，或者至少是否完全符合用户原始查询目标（用户原始查询才是最重要的！）
        2. 不符合输出: {{}}
        3. 符合则输出:

        {{
        "研究目的": "中文总结研究目的",
        "研究类型": "中文描述研究类型",
        "研究方法": "中文描述研究方法",
        "研究对象": "中文描述研究对象",
        "主要研究结果": "中文总结核心结果",
        "研究结论与意义": "中文总结结论和意义",
        "研究亮点或创新点": "中文提炼亮点，多点用分号分隔"
        }}
        """
        logger.info("Sending abstract to Gemini for screening and extraction based on complete PICO document.")
        return self._generate_content(prompt, use_pro_model=False)  # Use 2.5-flash-lite for article screening