#!/usr/bin/env python3
import logging
import json
import time
import concurrent.futures
from datetime import datetime
from dotenv import load_dotenv
import csv
import os

# Import modules from the 'modules' package
from modules.utils import setup_logging, load_retrieved_pmids, save_retrieved_pmids
from modules.gemini_client import GeminiClient
from modules.pubmed_client import PubMedClient
from modules.data_processor import (
    parse_pubmed_xml,
    extract_article_metadata,
    generate_formatted_excel,
)

# Configure logging at the very beginning
setup_logging()

# Load environment variables from .env file
load_dotenv()

MAX_REFINEMENT_ATTEMPTS = 299
PMID_CSV_FILE = "retrieved_pmids.csv"
MAX_STUDY_THRESHOLD = 3900
MIN_STUDY_THRESHOLD = 3000
PICO_TXT_FILE = "pico_standards.txt"
PICO_RESULTS_FILE = "pico_results.xlsx"

def save_pico_standard(pico_standard, user_query):
    """Saves the generated PICO standard to a TXT file, replacing any existing content."""
    try:
        # Always overwrite the file to ensure clean content for each new run
        with open(PICO_TXT_FILE, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("PICO标准文档\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"用户查询: {user_query}\n\n")
            f.write("PICO标准:\n")
            f.write("-" * 40 + "\n")
            f.write(f"{pico_standard}\n\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        logging.info(f"PICO standard saved to {PICO_TXT_FILE} (file replaced)")
    except IOError as e:
        logging.error(f"Failed to save PICO standard to {PICO_TXT_FILE}: {e}")

def load_pico_standard():
    """Loads the complete PICO standard file content."""
    try:
        with open(PICO_TXT_FILE, 'r', encoding='utf-8') as f:
            full_content = f.read().strip()

            # Extract user query for backward compatibility
            lines = full_content.split('\n')
            user_query = ""
            for line in lines:
                if line.startswith("用户查询:"):
                    user_query = line.replace("用户查询:", "").strip()
                    break

            return user_query, full_content
    except FileNotFoundError:
        logging.warning(f"PICO standard file {PICO_TXT_FILE} not found")
        return None, None
    except Exception as e:
        logging.error(f"Failed to load PICO standard from {PICO_TXT_FILE}: {e}")
        return None, None

def screen_article(article_info):
    """
    Helper function to screen a single article using Gemini.
    Designed to be used with a concurrent executor.
    """
    article_dict, pico_standard, user_query, gemini_client, current_results_count, current_attempt = article_info
    metadata = extract_article_metadata(article_dict)
    pmid = metadata.get("pmid", "N/A")

    if not metadata["abstract"] or metadata["abstract"] == "No abstract available.":
        logging.warning(f"Skipping article {pmid} due to missing abstract.")
        return None

    try:
        # The time.sleep is removed to maximize concurrency benefits.
        # The Gemini client already has tenacity for retries.
        extracted_info_str = gemini_client.extract_article_info(metadata["abstract"], pico_standard, user_query, current_results_count, current_attempt)

        # 清理Gemini响应中的markdown代码块标记
        cleaned_response = extracted_info_str.strip()
        if cleaned_response.startswith("```json"):
            cleaned_response = cleaned_response[7:]  # 移除开头的```json
        if cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-3]  # 移除结尾的```
        cleaned_response = cleaned_response.strip()

        extracted_info = json.loads(cleaned_response)

        if extracted_info:
            logging.info(f"Article {pmid} MATCHED PICO. Extracted details.")
            # Map the fields to new Chinese column names
            result = {
                "序号": "",  # 将在CSV生成时自动填充
                "期刊名称": metadata.get("journal"),
                "ISSN": metadata.get("issn"),
                "大类分区": "",  # 暂时留空
                "JCR分区": "",  # 暂时留空
                "最新IF": "",  # 暂时留空
                "5年IF": "",  # 暂时留空
                "排名": "",  # 暂时留空
                "PMID": metadata.get("pmid"),
                "PMCID": metadata.get("pmcid"),
                "文章标题": metadata.get("title"), # This was the bug, it should be assigned directly
                "第一作者": metadata.get("first_author"),
                "通讯作者": metadata.get("corresponding_author"),
                "第一作者单位": metadata.get("first_affiliation"),
                "发表时间": metadata.get("publication_date"),
                "研究目的": extracted_info.get("研究目的"),
                "研究类型": extracted_info.get("研究类型"),
                "研究方法": extracted_info.get("研究方法"),
                "研究对象": extracted_info.get("研究对象"),
                "主要研究结果": extracted_info.get("主要研究结果"),
                "研究结论与意义": extracted_info.get("研究结论与意义"),
                "研究亮点或创新点": extracted_info.get("研究亮点或创新点"),
            }
            return result
        else:
            logging.info(f"Article {pmid} did not match PICO standard. Skipping.")
            return None
    except json.JSONDecodeError:
        logging.error(f"Failed to decode JSON from Gemini response for article {pmid}. Original response: {extracted_info_str[:200]}...")
        logging.error(f"Cleaned response: {cleaned_response[:200]}...")
        return None
    except Exception as e:
        logging.error(f"An unexpected error occurred during screening of article {pmid}: {e}", exc_info=True)
        return None

def run_workflow(user_query: str):
    """
    Executes the full meta-analysis literature screening workflow.

    Args:
        user_query: The user's natural language research query.
    """
    logging.info(f"Workflow started for query: '{user_query}'")
    
    try:
        # --- 1. Initialize Clients and Load Existing Data ---
        gemini_client = GeminiClient()
        pubmed_client = PubMedClient()
        retrieved_pmids_set = load_retrieved_pmids(PMID_CSV_FILE)
        logging.info(f"Loaded {len(retrieved_pmids_set)} previously retrieved PMIDs.")
        
        all_screened_articles = [] # Initialize a list to accumulate all results

        # --- 2. Generate Initial PICO Standard ---
        logging.info("Step 1: Generating initial PICO standard...")
        pico_standard = gemini_client.generate_pico_standard(user_query)
        # pico_standard = "研究对象要求是精神障碍患者或精神动物模型，同时要求干预或对照手段是给予茶或茶提取物干预"
        # "研究对象要求是精神障碍患者或精神动物模型，要求干预或对照手段是给予茶或茶提取物干预"
        #"摘要中提及使用人工智能相关技术，特别是自然语言处理技术，并且研究对象要求是精神障碍出院后的病患"
        # "摘要中提及血脂与焦虑或抑郁的相关性，并且研究对象要求是二型糖尿病患者"
        
        # " 摘要中提及了精神心理医学相关数据库或数据集的名称和来源，注意数据库或数据集或研究人群主要来源于中国大陆，排除掉综述、系统综述和meta分析" # 可以来源于世界各地  ，注意数据库或数据集或研究人群主要来源于中国大陆


       #  pico_standard ="**P:** Any Human"\
# "**I:**  has a barbecue-style diet"\
# "**C:**  has a diet not characterized as barbecue-style "\
# "**O:**  Any Incidence of major depressive disorder,  severity of MDD symptoms, remission rates of MDD, and/or suicide attempts, etc."\


        # pico_standard =  "P: Any Adults (18-85 years old) "\
 # "I: Regular participation in organized or recreational sporting activities"\
 # "C:  Sedentary individuals with no regular sporting activities."\
 # "O: Incidence and prevalence of Major Depressive Disorder, or Depression Scale Score."\

        # pico_standard = "**P:** Adults (18-85 years) with a diagnosis of, or at risk for major depression disorder."\
# "**I:**  sports interventions aimed at modifying sports (e.g., Mediterranean diet, DASH diet, vegetarian diets, specific macronutrient restrictions or increases)."\
# "**C:**  Control group receiving no specific dietary intervention or following a standard/usual diet."\
# "**O:**  Incidence, prevalence, or severity of major depression disorder;  changes in symptoms scores related to mental health (using validated scales such as the Hamilton Depression Rating Scale, Beck Depression Inventory, PANSS);  remission rates; hospitalization rates."\


        # pico_standard ="**P:** Any human"\
# "**I:** Dietary interventions focusing on specific dietary patterns (e.g., Mediterranean diet, DASH diet, low-carbohydrate diet, whole-food plant-based diet) or macronutrient composition (e.g., high protein, high fat, high carbohydrate).  Interventions must be clearly defined and implemented."\
# "**C:**  Control groups receiving no specific dietary intervention, usual dietary intake, or alternative dietary approaches (e.g., placebo diet, standard dietary advice)."\
# "**O:**Any clinical quantitative  results"\

        # pico_standard ="**P:** Any human" \
        # "**I:** Artificial intelligence (AI) technologies (including but not limited to machine learning algorithms, natural language processing, computer vision) used to measure or predict Hamilton Depression Scale (HAM-D) scores." \
        # "**C:** any standard clinical assessment of Hamilton Depression score" \
        # "**O:** Accuracy (sensitivity, specificity, positive predictive value, negative predictive value), or Relevance, or reliability (inter-rater, test-retest), or validity (concurrent, predictive), or efficiency (time taken for assessment) of AI-based measurement compared to standard clinical assessment" \

        # Accuracy (sensitivity, specificity, positive predictive value, negative predictive value), or reliability (inter-rater, test-retest), or validity (concurrent, predictive), or efficiency (time taken for assessment) of AI-based HAM-A measurement compared to standard clinical assessment.
# Any quantitative results

        # pico_standard ="P: patients with mental illness or psychiatric disorder receiving medication" \
        # "I: D-dimer Monitoring" \
        #  " C:  Any control group" \
        #  " O: Any clinical results" \

       #  pico_standard = "* **P (Population):** Any humandiagnosed with Major Depressive Disorder (MDD)." \
# "* **I (Intervention):**Any Measurement of neurotransmitter levels in blood samples using mass spectrometry, including but not limited to, serotonin, dopamine, norepinephrine, and their metabolites." \
# "* **C (Comparison):** other diagnostic tools or measurement methods for depression." \
# "* **O (Outcome):** Any quantitative results." \



#  (specify medication classes if possible, e.g., antipsychotics, antidepressants, mood stabilizers).    Any clinically relevant outcomes   during medication treatment.
#  levels during medication treatment.    such as incidence of venous thromboembolism (VTE), mortality, hospital readmission rates, length of hospital stay, and adverse events related to VTE or medication.
#Absence of D-dimer monitoring

        logging.info(f"Generated Initial PICO Standard:\n{pico_standard}")
        save_pico_standard(pico_standard, user_query)
        # --- 3. Generate and Refine PubMed Query ---
        logging.info("Step 2: Generating PubMed search query...")
        current_query = gemini_client.generate_pubmed_query(user_query, pico_standard, attempt_number=1)

        for attempt in range(MAX_REFINEMENT_ATTEMPTS):
            current_attempt = attempt + 1  # 1-based attempt number
            logging.info(f"Search attempt {current_attempt}/{MAX_REFINEMENT_ATTEMPTS} with query: {current_query}")

            # 获取当前已筛选的文献数量（用于第4轮开始的自适应策略）
            try:
                import pandas as pd
                current_results_count = len(pd.read_excel(PICO_RESULTS_FILE))
            except FileNotFoundError:
                current_results_count = 0

            # 记录当前策略阶段
            if current_attempt <= 3:
                logging.info(f"Phase 1 (Round {current_attempt}/3): Loose search + Strict PICO screening")
            else:
                logging.info(f"Phase 2 (Round {current_attempt}): Adaptive strategy based on {current_results_count} existing results")

            # --- 4. Two-Step Strategy: Get All PMIDs First ---
            search_result = pubmed_client.esearch(current_query)
            current_count = search_result.get("count", 0) if search_result else 0

            if not search_result or current_count == 0:
                logging.warning(f"Search attempt {current_attempt} yielded no results. Refining query...")
                if attempt < MAX_REFINEMENT_ATTEMPTS - 1:
                    current_query = gemini_client.refine_pubmed_query(user_query, pico_standard, current_query, current_count, current_attempt, current_results_count)
                    continue
                else:
                    logging.error("Maximum refinement attempts reached. Could not find any articles. Exiting.")
                    return

            logging.info(f"Search successful! Found {current_count} articles.")

            # Get all PMIDs from the search result
            all_pmids = search_result.get("idlist", [])
            logging.info(f"Retrieved {len(all_pmids)} PMIDs from search result.")

            # Calculate difference with already processed PMIDs
            new_pmids = [pmid for pmid in all_pmids if pmid not in retrieved_pmids_set]
            logging.info(f"Found {len(new_pmids)} new PMIDs after excluding already processed ones.")

            if not new_pmids:
                logging.info("No new PMIDs to process in this batch. Continuing to next refinement.")
                if attempt < MAX_REFINEMENT_ATTEMPTS - 1:
                    current_query = gemini_client.refine_pubmed_query(user_query, pico_standard, current_query, current_count, current_attempt, current_results_count)
                    continue
                else:
                    logging.warning("No new PMIDs found even after maximum refinement attempts.")
                    break

            # Take first 600 PMIDs for efetch
            pmids_to_fetch = new_pmids[:600]
            logging.info(f"Selecting first {len(pmids_to_fetch)} PMIDs for detailed fetching.")

            # --- 5. Fetch Article Data for Selected PMIDs ---
            xml_data = pubmed_client.efetch_by_pmids(pmids_to_fetch)
            if not xml_data:
                logging.error("Failed to fetch XML data from PubMed. Exiting.")
                return

            all_articles = parse_pubmed_xml(xml_data)
            logging.info(f"Successfully fetched and parsed {len(all_articles)} articles from PubMed.")

            # --- 6. Concurrent Screening ---
            # All articles should be new since we already filtered PMIDs
            articles_to_process = all_articles

            logging.info(f"Starting concurrent screening of {len(articles_to_process)} new articles...")

            # 记录筛选策略
            if current_attempt <= 3:
                logging.info(f"Screening strategy: Strict PICO compliance (Phase 1, Round {current_attempt})")
            else:
                if current_results_count < 20:
                    logging.info(f"Screening strategy: Relaxed PICO (Phase 2, {current_results_count} results - need more)")
                elif current_results_count < 100:
                    logging.info(f"Screening strategy: Standard PICO (Phase 2, {current_results_count} results - balanced)")
                else:
                    logging.info(f"Screening strategy: Strict PICO (Phase 2, {current_results_count} results - sufficient)")

            screened_articles = []
            tasks = [(article, pico_standard, user_query, gemini_client, current_results_count, current_attempt) for article in articles_to_process]

            with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
                results = executor.map(screen_article, tasks)
                for result in results:
                    if result:
                        screened_articles.append(result)

            # Update the master PMID list immediately after screening this batch
            # We update with the PMIDs we actually fetched and processed
            processed_pmids = set(pmids_to_fetch)
            retrieved_pmids_set.update(processed_pmids)
            save_retrieved_pmids(PMID_CSV_FILE, retrieved_pmids_set)
            logging.info(f"Updated master PMID list with {len(processed_pmids)} new PMIDs.")

            # --- Data Persistence ---
            if screened_articles:
                all_screened_articles.extend(screened_articles)
                logging.info(f"Accumulated {len(screened_articles)} new articles. Total so far: {len(all_screened_articles)}.")
                # The path to the reference file
                reference_file_path = os.path.join('modules', '2024影响因子+2025年中科院分区.csv')
                # Generate the Excel file with all accumulated articles
                generate_formatted_excel(all_screened_articles, reference_file_path, PICO_RESULTS_FILE)
            
            # --- Decision Logic ---
            # Read the total number of articles from the Excel file for decision making
            try:
                import pandas as pd
                total_included = len(pd.read_excel(PICO_RESULTS_FILE))
            except FileNotFoundError:
                total_included = 0
            
            logging.info(f"Screening finished for this batch. Total matching articles so far: {total_included}")


            if total_included >= MIN_STUDY_THRESHOLD:
                logging.info(f"Goal achieved! Found {total_included} studies, which meets the minimum threshold of {MIN_STUDY_THRESHOLD}.")
                
                # --- Final PICO Refinement if Necessary ---
                if total_included > MAX_STUDY_THRESHOLD:
                    logging.warning(f"Number of studies ({total_included}) exceeds the max threshold ({MAX_STUDY_THRESHOLD}). Refining PICO...")
                    pico_standard = gemini_client.refine_pico_standard(user_query, pico_standard, total_included, MAX_STUDY_THRESHOLD)
                    # A full implementation might re-run screening with the new PICO.
                    # For now, we will just log the refined PICO and exit.
                    logging.info(f"A more refined PICO has been generated. You may want to re-run the process with it.")

                logging.info(f"Final results are in {PICO_RESULTS_FILE}")
                break  # Exit the loop
            else:
                logging.warning(f"Found only {total_included} studies, which is below the minimum of {MIN_STUDY_THRESHOLD}. Refining PubMed query for next attempt...")
                if attempt < MAX_REFINEMENT_ATTEMPTS - 1:
                    current_query = gemini_client.refine_pubmed_query(user_query, pico_standard, current_query, current_count, current_attempt, current_results_count)
                else:
                    logging.error(f"Could not find enough articles even after maximum refinement attempts. Final results are in {PICO_RESULTS_FILE}.")
                    return

        logging.info("Workflow completed successfully!")

    except Exception as e:
        logging.critical(f"A critical error occurred during the workflow: {e}", exc_info=True)


if __name__ == "__main__":
    # This makes the script runnable from the command line.
    # Example usage: python main.py "Your research topic here"
    import sys
    if len(sys.argv) > 1:
        # Join all arguments after the script name to form the query
        query = " ".join(sys.argv[1:])
        run_workflow(query)
    else:
        # Example query if none is provided
        print("Usage: python main.py <your_research_query>")
        print("\\nRunning with an example query...")
        example_query = "茶与精神障碍的相关性"
        # "茶与抑郁症的相关性"
        # "人工智能相关技术是否可以辅助降低重性精神障碍复发率或者辅助预后随访"
        # "血脂与焦虑抑郁的相关性是什么"
        # 是否有精神心理医学相关的中国据库或者数据集
        # "Is Barbecue diet related to prevention or risk of major depression disorders"
        # "Can artificial intelligence technologies assist in the measurement of Hamilton Depression scale"  
        # The value of combined markers of neurotransmitters in blood based on mass spectrometry in the diagnosis of depression
        # dose D-dimer measuremnt during medication in mental patients or psychiatric patients has benefit
        # Can artificial intelligence technologies assist in the measurement of Hamilton Depression scale
        #  人工智能相关技术是否可以辅助进行汉密尔顿焦虑量表测量
        # Is time sequence analysis or natural language processing playing a role in the field of  sleeping disorders
        #  Can Artificial Intelligent technology assist in depression measurement  
        # Can natural language processing technology assist in prognosis rehabilitation and follow-up of discharged patients with depression?
        # Is artificial intelligence playing a role in the field of outcomeprediction and identification between depression and bipolar disorder
        run_workflow(example_query)