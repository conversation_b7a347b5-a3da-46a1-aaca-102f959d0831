2025-08-05 22:52:26 - root - INFO - Logging configured successfully.
2025-08-05 22:52:26 - root - INFO - Workflow started for query: '请帮我检索nAMD病程中探究光感受器损伤与修复的相关的文献,我只要临床研究,不要通路研究'
2025-08-05 22:52:26 - modules.gemini_client - INFO - Google Gemini client initialized successfully with gemini-2.5-flash-lite for query and screening tasks.
2025-08-05 22:52:26 - modules.pubmed_client - INFO - PubMed client initialized with an API key.
2025-08-05 22:52:26 - root - INFO - PMID file not found at 'retrieved_pmids.csv'. Starting with an empty set.
2025-08-05 22:52:26 - root - INFO - Loaded 0 previously retrieved PMIDs.
2025-08-05 22:52:26 - root - INFO - Step 1: Generating initial PICO standard...
2025-08-05 22:52:26 - modules.gemini_client - INFO - Generating PICO standard for query: '请帮我检索nAMD病程中探究光感受器损伤与修复的相关的文献,我只要临床研究,不要通路研究'
2025-08-05 22:52:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:52:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:52:54 - root - INFO - Generated Initial PICO Standard:
PICO标准:
----------------------------------------
P (Population/Problem):
- 新生血管性年龄相关性黄斑变性 (neovascular Age-related Macular Degeneration, nAMD) 患者

I (Intervention):
- 针对nAMD的任何治疗干预 (例如: anti-VEGF 治疗)

C (Comparison):
- 安慰剂、假性注射 (sham injection)、标准治疗或其他活性对照

O (Outcome):
- 评估光感受器结构损伤与修复的指标，主要为通过光学相干断层扫描 (OCT) 测量的光感受器层 (如: 外界膜 - ELM, 椭圆体带 - EZ) 的完整性及其变化。
2025-08-05 22:52:54 - root - INFO - PICO standard saved to pico_standards.txt (file replaced)
2025-08-05 22:52:54 - root - INFO - Step 2: Generating PubMed search query...
2025-08-05 22:52:54 - modules.gemini_client - INFO - Generating initial PubMed query using comprehensive search instructions.
2025-08-05 22:52:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:52:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:52:56 - root - INFO - Search attempt 1/100 with query: (("non-arteritic anterior ischemic optic neuropathy"[Title/Abstract] OR "nAMD"[Title/Abstract]) AND ("photoreceptor"[Title/Abstract] OR "photoreceptor damage"[Title/Abstract] OR "photoreceptor degeneration"[Title/Abstract] OR "photoreceptor dysfunction"[Title/Abstract] OR "photoreceptor repair"[Title/Abstract] OR "photoreceptor restoration"[Title/Abstract]) AND ("disease progression"[Title/Abstract] OR "clinical course"[Title/Abstract] OR "pathogenesis"[Title/Abstract])) AND ("clinical study"[Publication Type] OR "clinical trial"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "retrospective study"[Title/Abstract] OR "prospective study"[Title/Abstract]) NOT ("pathway"[Title/Abstract] OR "molecular mechanism"[Title/Abstract] OR "biological pathway"[Title/Abstract] OR "signaling pathway"[Title/Abstract] OR "gene expression"[Title/Abstract])
2025-08-05 22:52:56 - root - INFO - Phase 1 (Round 1/3): Loose search + Strict PICO screening
2025-08-05 22:52:56 - modules.pubmed_client - INFO - Executing Esearch for term: (("non-arteritic anterior ischemic optic neuropathy"[Title/Abstract] OR "nAMD"[Title/Abstract]) AND ... (retmax=100000)
2025-08-05 22:52:56 - modules.pubmed_client - INFO - Esearch found 1 articles, retrieved 1 PMIDs.
2025-08-05 22:52:56 - root - INFO - Search successful! Found 1 articles.
2025-08-05 22:52:56 - root - INFO - Retrieved 1 PMIDs from search result.
2025-08-05 22:52:56 - root - INFO - Found 1 new PMIDs after excluding already processed ones.
2025-08-05 22:52:56 - root - INFO - Selecting first 1 PMIDs for detailed fetching.
2025-08-05 22:52:56 - modules.pubmed_client - INFO - Executing Efetch for 1 specific PMIDs using POST request
2025-08-05 22:52:57 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:52:57 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:52:57 - modules.data_processor - INFO - Successfully parsed 1 articles from XML.
2025-08-05 22:52:57 - root - INFO - Successfully fetched and parsed 1 articles from PubMed.
2025-08-05 22:52:57 - root - INFO - Starting concurrent screening of 1 new articles...
2025-08-05 22:52:57 - root - INFO - Screening strategy: Strict PICO compliance (Phase 1, Round 1)
2025-08-05 22:52:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:52:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:52:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:52:59 - root - INFO - Article 31047330 MATCHED PICO. Extracted details.
2025-08-05 22:52:59 - root - INFO - Successfully saved 1 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:52:59 - root - INFO - Updated master PMID list with 1 new PMIDs.
2025-08-05 22:52:59 - root - INFO - Accumulated 1 new articles. Total so far: 1.
2025-08-05 22:52:59 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:52:59 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:52:59 - modules.data_processor - INFO - Successfully saved 1 articles to pico_results.xlsx
2025-08-05 22:52:59 - root - INFO - Screening finished for this batch. Total matching articles so far: 1
2025-08-05 22:52:59 - root - WARNING - Found only 1 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:52:59 - modules.gemini_client - WARNING - Refining failed PubMed query: (("non-arteritic anterior ischemic optic neuropathy"[Title/Abstract] OR "nAMD"[Title/Abstract]) AND ("photoreceptor"[Title/Abstract] OR "photoreceptor damage"[Title/Abstract] OR "photoreceptor degeneration"[Title/Abstract] OR "photoreceptor dysfunction"[Title/Abstract] OR "photoreceptor repair"[Title/Abstract] OR "photoreceptor restoration"[Title/Abstract]) AND ("disease progression"[Title/Abstract] OR "clinical course"[Title/Abstract] OR "pathogenesis"[Title/Abstract])) AND ("clinical study"[Publication Type] OR "clinical trial"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "retrospective study"[Title/Abstract] OR "prospective study"[Title/Abstract]) NOT ("pathway"[Title/Abstract] OR "molecular mechanism"[Title/Abstract] OR "biological pathway"[Title/Abstract] OR "signaling pathway"[Title/Abstract] OR "gene expression"[Title/Abstract])
2025-08-05 22:52:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:00 - root - INFO - Search attempt 2/100 with query: (("non-arteritic anterior ischemic optic neuropathy"[MeSH Terms] OR "non-arteritic anterior ischemic optic neuropathy"[Title/Abstract] OR "nAI[N]ON"[Title/Abstract] OR "nAI[N]ON"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[Title/Abstract] OR "photoreceptor damage"[Title/Abstract] OR "photoreceptor degeneration"[Title/Abstract] OR "photoreceptor dysfunction"[Title/Abstract] OR "photoreceptor repair"[Title/Abstract] OR "photoreceptor restoration"[Title/Abstract] OR "photoreceptors"[tiab])) AND ("disease progression"[Title/Abstract] OR "disease progression"[MeSH Terms] OR "clinical course"[Title/Abstract] OR "pathogenesis"[Title/Abstract]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "controlled clinical trial"[Publication Type] OR "clinical study"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "retrospective study"[Title/Abstract] OR "prospective study"[Title/Abstract] OR "human"[MeSH Terms]) NOT ("pathway"[Title/Abstract] OR "pathways"[Title/Abstract] OR "molecular mechanism"[Title/Abstract] OR "biological pathway"[Title/Abstract] OR "signaling pathway"[Title/Abstract] OR "gene expression"[Title/Abstract])
2025-08-05 22:53:00 - root - INFO - Phase 1 (Round 2/3): Loose search + Strict PICO screening
2025-08-05 22:53:00 - modules.pubmed_client - INFO - Executing Esearch for term: (("non-arteritic anterior ischemic optic neuropathy"[MeSH Terms] OR "non-arteritic anterior ischemic... (retmax=100000)
2025-08-05 22:53:01 - modules.pubmed_client - INFO - Esearch found 0 articles, retrieved 0 PMIDs.
2025-08-05 22:53:01 - root - WARNING - Search attempt 2 yielded no results. Refining query...
2025-08-05 22:53:01 - modules.gemini_client - WARNING - Refining failed PubMed query: (("non-arteritic anterior ischemic optic neuropathy"[MeSH Terms] OR "non-arteritic anterior ischemic optic neuropathy"[Title/Abstract] OR "nAI[N]ON"[Title/Abstract] OR "nAI[N]ON"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[Title/Abstract] OR "photoreceptor damage"[Title/Abstract] OR "photoreceptor degeneration"[Title/Abstract] OR "photoreceptor dysfunction"[Title/Abstract] OR "photoreceptor repair"[Title/Abstract] OR "photoreceptor restoration"[Title/Abstract] OR "photoreceptors"[tiab])) AND ("disease progression"[Title/Abstract] OR "disease progression"[MeSH Terms] OR "clinical course"[Title/Abstract] OR "pathogenesis"[Title/Abstract]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "controlled clinical trial"[Publication Type] OR "clinical study"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "retrospective study"[Title/Abstract] OR "prospective study"[Title/Abstract] OR "human"[MeSH Terms]) NOT ("pathway"[Title/Abstract] OR "pathways"[Title/Abstract] OR "molecular mechanism"[Title/Abstract] OR "biological pathway"[Title/Abstract] OR "signaling pathway"[Title/Abstract] OR "gene expression"[Title/Abstract])
2025-08-05 22:53:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:02 - root - INFO - Search attempt 3/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[Title/Abstract] OR "AMD"[Title/Abstract] OR "nAMD"[Title/Abstract]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[Title/Abstract] OR "photoreceptor damage"[Title/Abstract] OR "photoreceptor degeneration"[Title/Abstract] OR "photoreceptor dysfunction"[Title/Abstract] OR "photoreceptor repair"[Title/Abstract] OR "photoreceptor restoration"[Title/Abstract]) AND ("disease progression"[Title/Abstract] OR "disease progression"[MeSH Terms] OR "clinical course"[Title/Abstract]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "controlled clinical trial"[Publication Type] OR "clinical study"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "retrospective study"[Title/Abstract] OR "prospective study"[Title/Abstract] OR "human"[MeSH Terms]) NOT ("pathway"[Title/Abstract] OR "pathways"[Title/Abstract] OR "molecular mechanism"[Title/Abstract] OR "biological pathway"[Title/Abstract] OR "signaling pathway"[Title/Abstract] OR "gene expression"[Title/Abstract] OR "mechanism"[Title/Abstract] OR "pathogenesis"[Title/Abstract]))
2025-08-05 22:53:02 - root - INFO - Phase 1 (Round 3/3): Loose search + Strict PICO screening
2025-08-05 22:53:02 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[Title/Abstrac... (retmax=100000)
2025-08-05 22:53:03 - modules.pubmed_client - INFO - Esearch found 14 articles, retrieved 14 PMIDs.
2025-08-05 22:53:03 - root - INFO - Search successful! Found 14 articles.
2025-08-05 22:53:03 - root - INFO - Retrieved 14 PMIDs from search result.
2025-08-05 22:53:03 - root - INFO - Found 13 new PMIDs after excluding already processed ones.
2025-08-05 22:53:03 - root - INFO - Selecting first 13 PMIDs for detailed fetching.
2025-08-05 22:53:03 - modules.pubmed_client - INFO - Executing Efetch for 13 specific PMIDs using POST request
2025-08-05 22:53:03 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:53:03 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:53:03 - modules.data_processor - INFO - Successfully parsed 13 articles from XML.
2025-08-05 22:53:03 - root - INFO - Successfully fetched and parsed 13 articles from PubMed.
2025-08-05 22:53:03 - root - INFO - Starting concurrent screening of 13 new articles...
2025-08-05 22:53:03 - root - INFO - Screening strategy: Strict PICO compliance (Phase 1, Round 3)
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:04 - root - INFO - Article 35948209 did not match PICO standard. Skipping.
2025-08-05 22:53:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:04 - root - INFO - Article 27591785 did not match PICO standard. Skipping.
2025-08-05 22:53:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:04 - root - INFO - Article 28847641 did not match PICO standard. Skipping.
2025-08-05 22:53:05 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:05 - root - INFO - Article 29618560 MATCHED PICO. Extracted details.
2025-08-05 22:53:05 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:05 - root - INFO - Article 31281056 MATCHED PICO. Extracted details.
2025-08-05 22:53:05 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:05 - root - INFO - Article 23883530 MATCHED PICO. Extracted details.
2025-08-05 22:53:05 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:05 - root - INFO - Article 39833572 MATCHED PICO. Extracted details.
2025-08-05 22:53:05 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:05 - root - INFO - Article 35667569 MATCHED PICO. Extracted details.
2025-08-05 22:53:06 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:06 - root - INFO - Article 39028907 MATCHED PICO. Extracted details.
2025-08-05 22:53:06 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:06 - root - INFO - Article 27409486 MATCHED PICO. Extracted details.
2025-08-05 22:53:06 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:06 - root - INFO - Article 31924545 MATCHED PICO. Extracted details.
2025-08-05 22:53:06 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:06 - root - INFO - Article 23764969 MATCHED PICO. Extracted details.
2025-08-05 22:53:06 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:06 - root - INFO - Article 24907433 MATCHED PICO. Extracted details.
2025-08-05 22:53:06 - root - INFO - Successfully saved 14 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:53:06 - root - INFO - Updated master PMID list with 13 new PMIDs.
2025-08-05 22:53:06 - root - INFO - Accumulated 10 new articles. Total so far: 11.
2025-08-05 22:53:06 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:53:06 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:53:06 - modules.data_processor - INFO - Successfully saved 11 articles to pico_results.xlsx
2025-08-05 22:53:06 - root - INFO - Screening finished for this batch. Total matching articles so far: 11
2025-08-05 22:53:06 - root - WARNING - Found only 11 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:53:06 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[Title/Abstract] OR "AMD"[Title/Abstract] OR "nAMD"[Title/Abstract]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[Title/Abstract] OR "photoreceptor damage"[Title/Abstract] OR "photoreceptor degeneration"[Title/Abstract] OR "photoreceptor dysfunction"[Title/Abstract] OR "photoreceptor repair"[Title/Abstract] OR "photoreceptor restoration"[Title/Abstract]) AND ("disease progression"[Title/Abstract] OR "disease progression"[MeSH Terms] OR "clinical course"[Title/Abstract]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "controlled clinical trial"[Publication Type] OR "clinical study"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "retrospective study"[Title/Abstract] OR "prospective study"[Title/Abstract] OR "human"[MeSH Terms]) NOT ("pathway"[Title/Abstract] OR "pathways"[Title/Abstract] OR "molecular mechanism"[Title/Abstract] OR "biological pathway"[Title/Abstract] OR "signaling pathway"[Title/Abstract] OR "gene expression"[Title/Abstract] OR "mechanism"[Title/Abstract] OR "pathogenesis"[Title/Abstract]))
2025-08-05 22:53:06 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:07 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:07 - root - INFO - Search attempt 4/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor degeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor restoration"[tiab]) AND ("disease progression"[tiab] OR "disease progression"[MeSH Terms] OR "clinical course"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical study"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "signaling pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "pathophysiology"[tiab]))
2025-08-05 22:53:07 - root - INFO - Phase 2 (Round 4): Adaptive strategy based on 11 existing results
2025-08-05 22:53:07 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD... (retmax=100000)
2025-08-05 22:53:08 - modules.pubmed_client - INFO - Esearch found 14 articles, retrieved 14 PMIDs.
2025-08-05 22:53:08 - root - INFO - Search successful! Found 14 articles.
2025-08-05 22:53:08 - root - INFO - Retrieved 14 PMIDs from search result.
2025-08-05 22:53:08 - root - INFO - Found 0 new PMIDs after excluding already processed ones.
2025-08-05 22:53:08 - root - INFO - No new PMIDs to process in this batch. Continuing to next refinement.
2025-08-05 22:53:08 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor degeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor restoration"[tiab]) AND ("disease progression"[tiab] OR "disease progression"[MeSH Terms] OR "clinical course"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical study"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "signaling pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "pathophysiology"[tiab]))
2025-08-05 22:53:08 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:09 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:09 - root - INFO - Search attempt 5/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab] OR "macular degeneration, age-related"[MeSH Terms]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor degeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor restoration"[tiab] OR "cone cell"[tiab] OR "rod cell"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("disease progression"[tiab] OR "disease progression"[MeSH Terms] OR "clinical course"[tiab] OR "progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "advancement"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical study"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms] OR "humans"[tiab]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "signaling pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "pathophysiology"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab]))
2025-08-05 22:53:09 - root - INFO - Phase 2 (Round 5): Adaptive strategy based on 11 existing results
2025-08-05 22:53:09 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD... (retmax=100000)
2025-08-05 22:53:10 - modules.pubmed_client - INFO - Esearch found 93 articles, retrieved 93 PMIDs.
2025-08-05 22:53:10 - root - INFO - Search successful! Found 93 articles.
2025-08-05 22:53:10 - root - INFO - Retrieved 93 PMIDs from search result.
2025-08-05 22:53:10 - root - INFO - Found 79 new PMIDs after excluding already processed ones.
2025-08-05 22:53:10 - root - INFO - Selecting first 79 PMIDs for detailed fetching.
2025-08-05 22:53:10 - modules.pubmed_client - INFO - Executing Efetch for 79 specific PMIDs using POST request
2025-08-05 22:53:11 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:53:11 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:53:11 - modules.data_processor - INFO - Successfully parsed 79 articles from XML.
2025-08-05 22:53:11 - root - INFO - Successfully fetched and parsed 79 articles from PubMed.
2025-08-05 22:53:11 - root - INFO - Starting concurrent screening of 79 new articles...
2025-08-05 22:53:11 - root - INFO - Screening strategy: Relaxed PICO (Phase 2, 11 results - need more)
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:11 - root - INFO - Article 40474962 did not match PICO standard. Skipping.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:11 - root - INFO - Article 39080402 did not match PICO standard. Skipping.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - root - INFO - Article 40762815 did not match PICO standard. Skipping.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:11 - root - INFO - Article 40557781 did not match PICO standard. Skipping.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:11 - root - INFO - Article 38219789 did not match PICO standard. Skipping.
2025-08-05 22:53:11 - root - INFO - Article 37097804 did not match PICO standard. Skipping.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:11 - root - INFO - Article 38289348 did not match PICO standard. Skipping.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:11 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:11 - root - INFO - Article 35935092 did not match PICO standard. Skipping.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:11 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:12 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:12 - root - INFO - Article 40048184 did not match PICO standard. Skipping.
2025-08-05 22:53:12 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:12 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:12 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:12 - root - INFO - Article 34264377 did not match PICO standard. Skipping.
2025-08-05 22:53:12 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:12 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:12 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:12 - root - INFO - Article 33605982 did not match PICO standard. Skipping.
2025-08-05 22:53:12 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:12 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - root - INFO - Article 38484787 MATCHED PICO. Extracted details.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - root - INFO - Article 35853489 MATCHED PICO. Extracted details.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - root - INFO - Article 39151755 MATCHED PICO. Extracted details.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - root - INFO - Article 32243712 did not match PICO standard. Skipping.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - root - INFO - Article 39818249 MATCHED PICO. Extracted details.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - root - INFO - Article 36284220 MATCHED PICO. Extracted details.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - root - INFO - Article 32088159 did not match PICO standard. Skipping.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - root - INFO - Article 39987980 MATCHED PICO. Extracted details.
2025-08-05 22:53:13 - root - INFO - Article 34173365 MATCHED PICO. Extracted details.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:13 - root - INFO - Article 31958105 did not match PICO standard. Skipping.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 38471039 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 35654365 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 30809263 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 32533046 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 30142373 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 29621509 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 39538001 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 40047020 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 30129371 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 29259393 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 35853434 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 29553577 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 35113137 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 28506260 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 33750892 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 34153289 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 35031679 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 35029632 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 32409980 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 27149697 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 27142805 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 38944135 MATCHED PICO. Extracted details.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 26545317 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 25515578 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:14 - root - INFO - Article 27110092 did not match PICO standard. Skipping.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:15 - root - INFO - Article 25198798 did not match PICO standard. Skipping.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:15 - root - INFO - Article 26432927 did not match PICO standard. Skipping.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:15 - root - INFO - Article 23622873 did not match PICO standard. Skipping.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:15 - root - INFO - Article 32402555 MATCHED PICO. Extracted details.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:15 - root - INFO - Article 32191267 MATCHED PICO. Extracted details.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:15 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 31661979 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 25392267 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 27417506 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 26578448 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 31842189 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 30310161 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - root - INFO - Article 19752426 did not match PICO standard. Skipping.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 23352193 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 19752423 did not match PICO standard. Skipping.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 28153442 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 16613501 did not match PICO standard. Skipping.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 19091420 did not match PICO standard. Skipping.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 28166160 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 11933894 did not match PICO standard. Skipping.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 15803179 did not match PICO standard. Skipping.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 27135213 MATCHED PICO. Extracted details.
2025-08-05 22:53:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:16 - root - INFO - Article 26747761 MATCHED PICO. Extracted details.
2025-08-05 22:53:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:17 - root - INFO - Article 27115852 MATCHED PICO. Extracted details.
2025-08-05 22:53:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:17 - root - INFO - Article 23132511 MATCHED PICO. Extracted details.
2025-08-05 22:53:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:17 - root - INFO - Article 27488150 MATCHED PICO. Extracted details.
2025-08-05 22:53:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:17 - root - INFO - Article 25277305 MATCHED PICO. Extracted details.
2025-08-05 22:53:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:17 - root - INFO - Article 23652578 MATCHED PICO. Extracted details.
2025-08-05 22:53:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:17 - root - INFO - Article 24881600 MATCHED PICO. Extracted details.
2025-08-05 22:53:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:17 - root - INFO - Article 22030354 MATCHED PICO. Extracted details.
2025-08-05 22:53:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:18 - root - INFO - Article 16399908 MATCHED PICO. Extracted details.
2025-08-05 22:53:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:18 - root - INFO - Article 23349430 MATCHED PICO. Extracted details.
2025-08-05 22:53:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:18 - root - INFO - Article 20079925 MATCHED PICO. Extracted details.
2025-08-05 22:53:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:18 - root - INFO - Article 20543764 MATCHED PICO. Extracted details.
2025-08-05 22:53:18 - root - INFO - Successfully saved 93 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:53:18 - root - INFO - Updated master PMID list with 79 new PMIDs.
2025-08-05 22:53:18 - root - INFO - Accumulated 44 new articles. Total so far: 55.
2025-08-05 22:53:18 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:53:18 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:53:18 - modules.data_processor - INFO - Successfully saved 55 articles to pico_results.xlsx
2025-08-05 22:53:18 - root - INFO - Screening finished for this batch. Total matching articles so far: 55
2025-08-05 22:53:18 - root - WARNING - Found only 55 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:53:18 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab] OR "macular degeneration, age-related"[MeSH Terms]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor degeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor restoration"[tiab] OR "cone cell"[tiab] OR "rod cell"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("disease progression"[tiab] OR "disease progression"[MeSH Terms] OR "clinical course"[tiab] OR "progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "advancement"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical study"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms] OR "humans"[tiab]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "signaling pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "pathophysiology"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab]))
2025-08-05 22:53:18 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:20 - root - INFO - Search attempt 6/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab] OR "macular degeneration, age-related"[MeSH Terms] OR "macular degeneration"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor degeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor restoration"[tiab] OR "cone cell"[tiab] OR "rod cell"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("disease progression"[tiab] OR "disease progression"[MeSH Terms] OR "clinical course"[tiab] OR "progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "advancement"[tiab] OR "natural history"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical study"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms] OR "humans"[tiab]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "signaling pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "pathophysiology"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab] OR "in vivo"[tiab]))
2025-08-05 22:53:20 - root - INFO - Phase 2 (Round 6): Adaptive strategy based on 55 existing results
2025-08-05 22:53:20 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD... (retmax=100000)
2025-08-05 22:53:20 - modules.pubmed_client - INFO - Esearch found 97 articles, retrieved 97 PMIDs.
2025-08-05 22:53:20 - root - INFO - Search successful! Found 97 articles.
2025-08-05 22:53:20 - root - INFO - Retrieved 97 PMIDs from search result.
2025-08-05 22:53:20 - root - INFO - Found 8 new PMIDs after excluding already processed ones.
2025-08-05 22:53:20 - root - INFO - Selecting first 8 PMIDs for detailed fetching.
2025-08-05 22:53:20 - modules.pubmed_client - INFO - Executing Efetch for 8 specific PMIDs using POST request
2025-08-05 22:53:21 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:53:21 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:53:21 - modules.data_processor - INFO - Successfully parsed 8 articles from XML.
2025-08-05 22:53:21 - root - INFO - Successfully fetched and parsed 8 articles from PubMed.
2025-08-05 22:53:21 - root - INFO - Starting concurrent screening of 8 new articles...
2025-08-05 22:53:21 - root - INFO - Screening strategy: Standard PICO (Phase 2, 55 results - balanced)
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:21 - root - INFO - Article 10852960 did not match PICO standard. Skipping.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:21 - root - INFO - Article 32727729 did not match PICO standard. Skipping.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:21 - root - INFO - Article 32251237 did not match PICO standard. Skipping.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:21 - root - INFO - Article 29293603 did not match PICO standard. Skipping.
2025-08-05 22:53:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:21 - root - INFO - Article 31174672 did not match PICO standard. Skipping.
2025-08-05 22:53:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:22 - root - INFO - Article 29884405 did not match PICO standard. Skipping.
2025-08-05 22:53:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:23 - root - INFO - Article 28475704 MATCHED PICO. Extracted details.
2025-08-05 22:53:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:24 - root - INFO - Article 31649003 MATCHED PICO. Extracted details.
2025-08-05 22:53:24 - root - INFO - Successfully saved 101 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:53:24 - root - INFO - Updated master PMID list with 8 new PMIDs.
2025-08-05 22:53:24 - root - INFO - Accumulated 2 new articles. Total so far: 57.
2025-08-05 22:53:24 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:53:24 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:53:24 - modules.data_processor - INFO - Successfully saved 57 articles to pico_results.xlsx
2025-08-05 22:53:24 - root - INFO - Screening finished for this batch. Total matching articles so far: 57
2025-08-05 22:53:24 - root - WARNING - Found only 57 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:53:24 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab] OR "macular degeneration, age-related"[MeSH Terms] OR "macular degeneration"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor degeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor restoration"[tiab] OR "cone cell"[tiab] OR "rod cell"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("disease progression"[tiab] OR "disease progression"[MeSH Terms] OR "clinical course"[tiab] OR "progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "advancement"[tiab] OR "natural history"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical study"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms] OR "humans"[tiab]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "signaling pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "pathophysiology"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab] OR "in vivo"[tiab]))
2025-08-05 22:53:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:25 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:25 - root - INFO - Search attempt 7/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "cone cells"[MeSH Terms] OR "rod cells"[MeSH Terms] OR "retinal photoreceptor cells"[tiab]) AND ("disease progression"[tiab] OR "clinical course"[tiab] OR "progression"[tiab] OR "deterioration"[tiab] OR "advancement"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab] OR "in vivo"[tiab]))
2025-08-05 22:53:25 - root - INFO - Phase 2 (Round 7): Adaptive strategy based on 57 existing results
2025-08-05 22:53:25 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD... (retmax=100000)
2025-08-05 22:53:26 - modules.pubmed_client - INFO - Esearch found 18 articles, retrieved 18 PMIDs.
2025-08-05 22:53:26 - root - INFO - Search successful! Found 18 articles.
2025-08-05 22:53:26 - root - INFO - Retrieved 18 PMIDs from search result.
2025-08-05 22:53:26 - root - INFO - Found 0 new PMIDs after excluding already processed ones.
2025-08-05 22:53:26 - root - INFO - No new PMIDs to process in this batch. Continuing to next refinement.
2025-08-05 22:53:26 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "cone cells"[MeSH Terms] OR "rod cells"[MeSH Terms] OR "retinal photoreceptor cells"[tiab]) AND ("disease progression"[tiab] OR "clinical course"[tiab] OR "progression"[tiab] OR "deterioration"[tiab] OR "advancement"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab] OR "in vivo"[tiab]))
2025-08-05 22:53:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:27 - root - INFO - Search attempt 8/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab] OR "senile macular degeneration"[tiab] OR "macular degeneration, age-related"[MeSH Terms]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "cone cells"[MeSH Terms] OR "rod cells"[MeSH Terms] OR "retinal photoreceptor cells"[tiab] OR "photoreceptor cell"[tiab] OR "cones"[tiab] OR "rods"[tiab]) AND ("disease progression"[tiab] OR "clinical course"[tiab] OR "progression"[tiab] OR "deterioration"[tiab] OR "advancement"[tiab] OR "course of disease"[tiab] OR "natural history"[tiab] OR "temporal pattern"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab] OR "in vivo"[tiab] OR "laboratory"[tiab] OR "preclinical"[tiab] OR "genetics"[tiab] OR "molecular"[tiab] OR "biochemistry"[tiab]))
2025-08-05 22:53:27 - root - INFO - Phase 2 (Round 8): Adaptive strategy based on 57 existing results
2025-08-05 22:53:27 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD... (retmax=100000)
2025-08-05 22:53:28 - modules.pubmed_client - INFO - Esearch found 51 articles, retrieved 51 PMIDs.
2025-08-05 22:53:28 - root - INFO - Search successful! Found 51 articles.
2025-08-05 22:53:28 - root - INFO - Retrieved 51 PMIDs from search result.
2025-08-05 22:53:28 - root - INFO - Found 32 new PMIDs after excluding already processed ones.
2025-08-05 22:53:28 - root - INFO - Selecting first 32 PMIDs for detailed fetching.
2025-08-05 22:53:28 - modules.pubmed_client - INFO - Executing Efetch for 32 specific PMIDs using POST request
2025-08-05 22:53:29 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:53:29 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:53:29 - modules.data_processor - INFO - Successfully parsed 32 articles from XML.
2025-08-05 22:53:29 - root - INFO - Successfully fetched and parsed 32 articles from PubMed.
2025-08-05 22:53:29 - root - INFO - Starting concurrent screening of 32 new articles...
2025-08-05 22:53:29 - root - INFO - Screening strategy: Standard PICO (Phase 2, 57 results - balanced)
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 40273531 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 39594569 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 38966602 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 38241770 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 39836449 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - root - INFO - Article 29296488 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 40171949 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 30185655 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 32691052 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 31051169 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 33847999 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 34110387 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 29361515 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 35042966 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 33520083 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 28625537 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 19608872 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 24550448 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 25273269 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 15452081 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 10562656 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 21854772 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 14764336 did not match PICO standard. Skipping.
2025-08-05 22:53:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:30 - root - INFO - Article 8751098 did not match PICO standard. Skipping.
2025-08-05 22:53:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:31 - root - INFO - Article 32870829 MATCHED PICO. Extracted details.
2025-08-05 22:53:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:31 - root - INFO - Article 39768707 MATCHED PICO. Extracted details.
2025-08-05 22:53:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:31 - root - INFO - Article 28083894 MATCHED PICO. Extracted details.
2025-08-05 22:53:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:31 - root - INFO - Article 37715860 MATCHED PICO. Extracted details.
2025-08-05 22:53:32 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:32 - root - INFO - Article 37314158 MATCHED PICO. Extracted details.
2025-08-05 22:53:32 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:32 - root - INFO - Article 25014365 MATCHED PICO. Extracted details.
2025-08-05 22:53:32 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:32 - root - INFO - Article 37502831 MATCHED PICO. Extracted details.
2025-08-05 22:53:32 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:32 - root - INFO - Article 26307399 MATCHED PICO. Extracted details.
2025-08-05 22:53:32 - root - INFO - Successfully saved 133 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:53:32 - root - INFO - Updated master PMID list with 32 new PMIDs.
2025-08-05 22:53:32 - root - INFO - Accumulated 8 new articles. Total so far: 65.
2025-08-05 22:53:32 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:53:32 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:53:32 - modules.data_processor - INFO - Successfully saved 65 articles to pico_results.xlsx
2025-08-05 22:53:32 - root - INFO - Screening finished for this batch. Total matching articles so far: 65
2025-08-05 22:53:32 - root - WARNING - Found only 65 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:53:32 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "age-related macular degeneration"[tiab] OR "AMD"[tiab] OR "nAMD"[tiab] OR "senile macular degeneration"[tiab] OR "macular degeneration, age-related"[MeSH Terms]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor"[tiab] OR "cone cells"[MeSH Terms] OR "rod cells"[MeSH Terms] OR "retinal photoreceptor cells"[tiab] OR "photoreceptor cell"[tiab] OR "cones"[tiab] OR "rods"[tiab]) AND ("disease progression"[tiab] OR "clinical course"[tiab] OR "progression"[tiab] OR "deterioration"[tiab] OR "advancement"[tiab] OR "course of disease"[tiab] OR "natural history"[tiab] OR "temporal pattern"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab] OR "in vivo"[tiab] OR "laboratory"[tiab] OR "preclinical"[tiab] OR "genetics"[tiab] OR "molecular"[tiab] OR "biochemistry"[tiab]))
2025-08-05 22:53:32 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:34 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:34 - root - INFO - Search attempt 9/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "retinal photoreceptor"[tiab]) AND ("disease progression"[tiab] OR "clinical course"[tiab] OR "progression"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "temporal progression"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab] OR "in vivo"[tiab] OR "laboratory"[tiab] OR "preclinical"[tiab] OR "genetics"[tiab] OR "molecular"[tiab] OR "biochemistry"[tiab]))
2025-08-05 22:53:34 - root - INFO - Phase 2 (Round 9): Adaptive strategy based on 65 existing results
2025-08-05 22:53:34 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:53:34 - modules.pubmed_client - INFO - Esearch found 0 articles, retrieved 0 PMIDs.
2025-08-05 22:53:34 - root - WARNING - Search attempt 9 yielded no results. Refining query...
2025-08-05 22:53:34 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "retinal photoreceptor"[tiab]) AND ("disease progression"[tiab] OR "clinical course"[tiab] OR "progression"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "temporal progression"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms]) NOT ("pathway"[tiab] OR "pathways"[tiab] OR "molecular mechanism"[tiab] OR "biological pathway"[tiab] OR "gene expression"[tiab] OR "mechanism"[tiab] OR "pathogenesis"[tiab] OR "cell culture"[pt] OR "in vitro"[tiab] OR "animal model"[tiab] OR "experimental study"[tiab] OR "in vivo"[tiab] OR "laboratory"[tiab] OR "preclinical"[tiab] OR "genetics"[tiab] OR "molecular"[tiab] OR "biochemistry"[tiab]))
2025-08-05 22:53:34 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:35 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:35 - root - INFO - Search attempt 10/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "retinal photoreceptor"[tiab]) AND ("clinical progression"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "progression"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "temporal progression"[tiab]) AND ("clinical trial"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms]))
2025-08-05 22:53:35 - root - INFO - Phase 2 (Round 10): Adaptive strategy based on 65 existing results
2025-08-05 22:53:35 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:53:36 - modules.pubmed_client - INFO - Esearch found 0 articles, retrieved 0 PMIDs.
2025-08-05 22:53:36 - root - WARNING - Search attempt 10 yielded no results. Refining query...
2025-08-05 22:53:36 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "retinal photoreceptor"[tiab]) AND ("clinical progression"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "progression"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "temporal progression"[tiab]) AND ("clinical trial"[pt] OR "observational study"[pt] OR "cohort study"[pt] OR "case series"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab] OR "human"[MeSH Terms]))
2025-08-05 22:53:36 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:36 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:36 - root - INFO - Search attempt 11/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab]) AND ("clinical trial"[pt] OR "observational study"[pt] OR "case report"[pt] OR "cohort study"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab]))
2025-08-05 22:53:36 - root - INFO - Phase 2 (Round 11): Adaptive strategy based on 65 existing results
2025-08-05 22:53:36 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:53:37 - modules.pubmed_client - INFO - Esearch found 0 articles, retrieved 0 PMIDs.
2025-08-05 22:53:37 - root - WARNING - Search attempt 11 yielded no results. Refining query...
2025-08-05 22:53:37 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab]) AND ("clinical trial"[pt] OR "observational study"[pt] OR "case report"[pt] OR "cohort study"[pt] OR "retrospective study"[tiab] OR "prospective study"[tiab]))
2025-08-05 22:53:37 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:38 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:38 - root - INFO - Search attempt 12/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab]))
2025-08-05 22:53:38 - root - INFO - Phase 2 (Round 12): Adaptive strategy based on 65 existing results
2025-08-05 22:53:38 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:53:38 - modules.pubmed_client - INFO - Esearch found 70 articles, retrieved 70 PMIDs.
2025-08-05 22:53:38 - root - INFO - Search successful! Found 70 articles.
2025-08-05 22:53:38 - root - INFO - Retrieved 70 PMIDs from search result.
2025-08-05 22:53:38 - root - INFO - Found 69 new PMIDs after excluding already processed ones.
2025-08-05 22:53:38 - root - INFO - Selecting first 69 PMIDs for detailed fetching.
2025-08-05 22:53:38 - modules.pubmed_client - INFO - Executing Efetch for 69 specific PMIDs using POST request
2025-08-05 22:53:40 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:53:40 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:53:40 - modules.data_processor - INFO - Successfully parsed 69 articles from XML.
2025-08-05 22:53:40 - root - INFO - Successfully fetched and parsed 69 articles from PubMed.
2025-08-05 22:53:40 - root - INFO - Starting concurrent screening of 69 new articles...
2025-08-05 22:53:40 - root - INFO - Screening strategy: Standard PICO (Phase 2, 65 results - balanced)
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 36626080 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - root - INFO - Article 36261438 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 37575875 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 36348407 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 37972750 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 37840667 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 39479233 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 40210893 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 37371481 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - root - INFO - Article 39337609 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - root - INFO - Article 39712068 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 38776811 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 36343937 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 38890703 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 37031820 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 35160044 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 35314673 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 33670685 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 34973334 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 33575853 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 33088172 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - root - INFO - Article 34911940 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 32041990 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 33848006 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:40 - root - INFO - Article 31712409 did not match PICO standard. Skipping.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 29060564 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - root - INFO - Article 29409883 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 29534078 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 28939808 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 29062223 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 30349463 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 28461502 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - root - INFO - Article 28607377 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - root - INFO - Article 28458639 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - root - INFO - Article 28351499 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 27831552 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 27151507 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 25711632 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 25447561 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:41 - root - INFO - Article 24812086 did not match PICO standard. Skipping.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 24664705 did not match PICO standard. Skipping.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 40228605 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 24509257 did not match PICO standard. Skipping.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 23601133 did not match PICO standard. Skipping.
2025-08-05 22:53:42 - root - INFO - Article 39660500 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - root - INFO - Article 32215180 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 36835257 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 37180103 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 31932580 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 31588172 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 21191711 did not match PICO standard. Skipping.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 22309197 did not match PICO standard. Skipping.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 37903056 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 19660665 did not match PICO standard. Skipping.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 16197342 did not match PICO standard. Skipping.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 28751206 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 31185022 MATCHED PICO. Extracted details.
2025-08-05 22:53:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:42 - root - INFO - Article 25188381 MATCHED PICO. Extracted details.
2025-08-05 22:53:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:43 - root - INFO - Article 30098196 MATCHED PICO. Extracted details.
2025-08-05 22:53:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:43 - root - INFO - Article 30995315 MATCHED PICO. Extracted details.
2025-08-05 22:53:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:43 - root - INFO - Article 25948251 MATCHED PICO. Extracted details.
2025-08-05 22:53:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:43 - root - INFO - Article 32670023 MATCHED PICO. Extracted details.
2025-08-05 22:53:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:43 - root - INFO - Article 28438165 MATCHED PICO. Extracted details.
2025-08-05 22:53:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:43 - root - INFO - Article 30126455 MATCHED PICO. Extracted details.
2025-08-05 22:53:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:43 - root - INFO - Article 23308196 MATCHED PICO. Extracted details.
2025-08-05 22:53:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:44 - root - INFO - Article 24626824 MATCHED PICO. Extracted details.
2025-08-05 22:53:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:44 - root - INFO - Article 23216380 MATCHED PICO. Extracted details.
2025-08-05 22:53:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:44 - root - INFO - Article 21179240 MATCHED PICO. Extracted details.
2025-08-05 22:53:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:45 - root - INFO - Article 19459869 MATCHED PICO. Extracted details.
2025-08-05 22:53:45 - root - INFO - Successfully saved 202 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:53:45 - root - INFO - Updated master PMID list with 69 new PMIDs.
2025-08-05 22:53:45 - root - INFO - Accumulated 22 new articles. Total so far: 87.
2025-08-05 22:53:45 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:53:45 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:53:45 - modules.data_processor - INFO - Successfully saved 87 articles to pico_results.xlsx
2025-08-05 22:53:45 - root - INFO - Screening finished for this batch. Total matching articles so far: 87
2025-08-05 22:53:45 - root - WARNING - Found only 87 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:53:45 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab]))
2025-08-05 22:53:45 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:46 - root - INFO - Search attempt 13/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab]) AND ("clinical trial"[Publication Type] OR "clinical study"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "human"[MeSH Terms]))
2025-08-05 22:53:46 - root - INFO - Phase 2 (Round 13): Adaptive strategy based on 87 existing results
2025-08-05 22:53:46 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:53:47 - modules.pubmed_client - INFO - Esearch found 3 articles, retrieved 3 PMIDs.
2025-08-05 22:53:47 - root - INFO - Search successful! Found 3 articles.
2025-08-05 22:53:47 - root - INFO - Retrieved 3 PMIDs from search result.
2025-08-05 22:53:47 - root - INFO - Found 1 new PMIDs after excluding already processed ones.
2025-08-05 22:53:47 - root - INFO - Selecting first 1 PMIDs for detailed fetching.
2025-08-05 22:53:47 - modules.pubmed_client - INFO - Executing Efetch for 1 specific PMIDs using POST request
2025-08-05 22:53:47 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:53:47 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:53:47 - modules.data_processor - INFO - Successfully parsed 1 articles from XML.
2025-08-05 22:53:47 - root - INFO - Successfully fetched and parsed 1 articles from PubMed.
2025-08-05 22:53:47 - root - INFO - Starting concurrent screening of 1 new articles...
2025-08-05 22:53:47 - root - INFO - Screening strategy: Standard PICO (Phase 2, 87 results - balanced)
2025-08-05 22:53:47 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:47 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:50 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:50 - root - INFO - Article 39694604 MATCHED PICO. Extracted details.
2025-08-05 22:53:50 - root - INFO - Successfully saved 203 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:53:50 - root - INFO - Updated master PMID list with 1 new PMIDs.
2025-08-05 22:53:50 - root - INFO - Accumulated 1 new articles. Total so far: 88.
2025-08-05 22:53:50 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:53:50 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:53:50 - modules.data_processor - INFO - Successfully saved 88 articles to pico_results.xlsx
2025-08-05 22:53:50 - root - INFO - Screening finished for this batch. Total matching articles so far: 88
2025-08-05 22:53:50 - root - WARNING - Found only 88 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:53:50 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab]) AND ("clinical trial"[Publication Type] OR "clinical study"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "human"[MeSH Terms]))
2025-08-05 22:53:50 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:51 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:51 - root - INFO - Search attempt 14/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab]) AND ("clinical trial"[Publication Type] OR "clinical study"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]))
2025-08-05 22:53:51 - root - INFO - Phase 2 (Round 14): Adaptive strategy based on 88 existing results
2025-08-05 22:53:51 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:53:52 - modules.pubmed_client - INFO - Esearch found 264 articles, retrieved 264 PMIDs.
2025-08-05 22:53:52 - root - INFO - Search successful! Found 264 articles.
2025-08-05 22:53:52 - root - INFO - Retrieved 264 PMIDs from search result.
2025-08-05 22:53:52 - root - INFO - Found 202 new PMIDs after excluding already processed ones.
2025-08-05 22:53:52 - root - INFO - Selecting first 202 PMIDs for detailed fetching.
2025-08-05 22:53:52 - modules.pubmed_client - INFO - Executing Efetch for 202 specific PMIDs using POST request
2025-08-05 22:53:53 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:53:53 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:53:54 - modules.data_processor - INFO - Successfully parsed 201 articles from XML.
2025-08-05 22:53:54 - root - INFO - Successfully fetched and parsed 201 articles from PubMed.
2025-08-05 22:53:54 - root - INFO - Starting concurrent screening of 201 new articles...
2025-08-05 22:53:54 - root - INFO - Screening strategy: Standard PICO (Phase 2, 88 results - balanced)
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 40364630 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 40581196 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 39930168 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 39419369 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 39604117 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 39640234 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 40158818 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 38983017 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 39930176 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 39394372 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 39104162 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 38874663 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 37806303 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 37610047 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 37691820 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 37531162 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 36986699 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 38983569 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - root - INFO - Article 36980486 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 36775060 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - root - INFO - Article 36638800 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 36570623 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:54 - root - INFO - Article 36204825 did not match PICO standard. Skipping.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 36014339 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - root - INFO - Article 36436549 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 35330834 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - root - INFO - Article 35758274 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - root - INFO - Article 35347235 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 36114218 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 35470760 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 36108103 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 35074938 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 34439501 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 34424232 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - root - INFO - Article 34263061 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - root - INFO - Article 34831174 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 34349498 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 34222230 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 34509423 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 33871679 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 34197777 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 33859044 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 33504896 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 38193957 MATCHED PICO. Extracted details.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 33390897 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 33049060 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 33022379 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 33122987 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 39430310 MATCHED PICO. Extracted details.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 32984302 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 32855763 did not match PICO standard. Skipping.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:55 - root - INFO - Article 39800286 MATCHED PICO. Extracted details.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 32831993 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 32641711 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 39768951 MATCHED PICO. Extracted details.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 32255762 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 32229292 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 32446735 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 38304945 MATCHED PICO. Extracted details.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 32068111 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 36553653 MATCHED PICO. Extracted details.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 37111251 MATCHED PICO. Extracted details.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 39605874 MATCHED PICO. Extracted details.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 31884670 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 31854278 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - root - INFO - Article 36877299 MATCHED PICO. Extracted details.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 31751761 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:56 - root - INFO - Article 31670434 did not match PICO standard. Skipping.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 37198519 MATCHED PICO. Extracted details.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 38980270 MATCHED PICO. Extracted details.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 31649499 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 31101737 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 31060031 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 31450606 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 30864945 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 30806237 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 31569695 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 31615975 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 33922669 MATCHED PICO. Extracted details.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 30581918 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 39342431 MATCHED PICO. Extracted details.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 33273666 MATCHED PICO. Extracted details.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - root - INFO - Article 30572343 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 30445193 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 33828232 MATCHED PICO. Extracted details.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 29762536 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 29038159 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 29291970 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 28974356 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 28858048 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 28846772 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - root - INFO - Article 28554397 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 28507131 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:57 - root - INFO - Article 27881906 did not match PICO standard. Skipping.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 26658483 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - root - INFO - Article 26694648 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 31885884 MATCHED PICO. Extracted details.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 26113213 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 33221285 MATCHED PICO. Extracted details.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 26237736 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - root - INFO - Article 26079476 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 32344157 MATCHED PICO. Extracted details.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 32209762 MATCHED PICO. Extracted details.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 26043704 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 25728228 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 26035859 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 25331497 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 25519068 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 32503052 MATCHED PICO. Extracted details.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 32789526 MATCHED PICO. Extracted details.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 25205867 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 24780906 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - root - INFO - Article 25103259 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 30867118 MATCHED PICO. Extracted details.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 24664714 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 32721592 MATCHED PICO. Extracted details.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:58 - root - INFO - Article 24160756 did not match PICO standard. Skipping.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 23631256 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 22823954 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 32366998 MATCHED PICO. Extracted details.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 22342521 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 22452753 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 22183388 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 29396515 MATCHED PICO. Extracted details.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 22178979 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 21757584 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 27164758 MATCHED PICO. Extracted details.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 21480547 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 27581213 MATCHED PICO. Extracted details.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 21191149 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 20931136 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 29392319 MATCHED PICO. Extracted details.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 30233136 MATCHED PICO. Extracted details.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 20920826 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 20220058 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 20096366 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 20206286 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 26754967 MATCHED PICO. Extracted details.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 24198525 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 20860013 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:53:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:53:59 - root - INFO - Article 19747980 did not match PICO standard. Skipping.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:53:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 19607829 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 19494642 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 19478335 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 19274853 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 18682809 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 20228934 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 18712653 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 18483561 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 27296489 MATCHED PICO. Extracted details.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 26655607 MATCHED PICO. Extracted details.
2025-08-05 22:54:00 - root - INFO - Article 17962484 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 18474255 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 17324604 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 18326691 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 17705997 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 17652758 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 17148042 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 25758815 MATCHED PICO. Extracted details.
2025-08-05 22:54:00 - root - INFO - Article 16303980 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - root - INFO - Article 15804841 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 27847624 MATCHED PICO. Extracted details.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 15369238 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 15177973 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 12634104 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 12610837 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 24206851 MATCHED PICO. Extracted details.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 25146987 MATCHED PICO. Extracted details.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 22930575 MATCHED PICO. Extracted details.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:00 - root - INFO - Article 12067593 did not match PICO standard. Skipping.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 23640042 MATCHED PICO. Extracted details.
2025-08-05 22:54:01 - root - INFO - Article 22220722 MATCHED PICO. Extracted details.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 11853755 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 11934323 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 11131418 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 10690836 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 11006290 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 9022310 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 9040481 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 8794711 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 8602790 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 7710404 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 2309857 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 23288902 MATCHED PICO. Extracted details.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 2610169 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 7692366 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 2759786 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 18046922 MATCHED PICO. Extracted details.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 6682096 did not match PICO standard. Skipping.
2025-08-05 22:54:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:01 - root - INFO - Article 21457933 MATCHED PICO. Extracted details.
2025-08-05 22:54:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:02 - root - INFO - Article 21764030 MATCHED PICO. Extracted details.
2025-08-05 22:54:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:02 - root - INFO - Article 15365174 MATCHED PICO. Extracted details.
2025-08-05 22:54:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:02 - root - INFO - Article 18509668 MATCHED PICO. Extracted details.
2025-08-05 22:54:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:02 - root - INFO - Article 19410953 MATCHED PICO. Extracted details.
2025-08-05 22:54:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:02 - root - INFO - Article 15682400 MATCHED PICO. Extracted details.
2025-08-05 22:54:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:02 - root - INFO - Article 12172115 MATCHED PICO. Extracted details.
2025-08-05 22:54:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:03 - root - INFO - Article 12427055 MATCHED PICO. Extracted details.
2025-08-05 22:54:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:03 - root - INFO - Article 12937992 MATCHED PICO. Extracted details.
2025-08-05 22:54:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:03 - root - INFO - Article 12556422 MATCHED PICO. Extracted details.
2025-08-05 22:54:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:03 - root - INFO - Article 3447341 MATCHED PICO. Extracted details.
2025-08-05 22:54:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:04 - root - INFO - Article 11222543 MATCHED PICO. Extracted details.
2025-08-05 22:54:04 - root - INFO - Successfully saved 405 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:54:04 - root - INFO - Updated master PMID list with 202 new PMIDs.
2025-08-05 22:54:04 - root - INFO - Accumulated 53 new articles. Total so far: 141.
2025-08-05 22:54:04 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:54:04 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:54:04 - modules.data_processor - INFO - Successfully saved 141 articles to pico_results.xlsx
2025-08-05 22:54:04 - root - INFO - Screening finished for this batch. Total matching articles so far: 141
2025-08-05 22:54:04 - root - WARNING - Found only 141 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:54:04 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab]) AND ("clinical trial"[Publication Type] OR "clinical study"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]))
2025-08-05 22:54:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:05 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:05 - root - INFO - Search attempt 15/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]))
2025-08-05 22:54:05 - root - INFO - Phase 2 (Round 15): Adaptive strategy based on 141 existing results
2025-08-05 22:54:05 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:54:06 - modules.pubmed_client - INFO - Esearch found 264 articles, retrieved 264 PMIDs.
2025-08-05 22:54:06 - root - INFO - Search successful! Found 264 articles.
2025-08-05 22:54:06 - root - INFO - Retrieved 264 PMIDs from search result.
2025-08-05 22:54:06 - root - INFO - Found 0 new PMIDs after excluding already processed ones.
2025-08-05 22:54:06 - root - INFO - No new PMIDs to process in this batch. Continuing to next refinement.
2025-08-05 22:54:06 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]))
2025-08-05 22:54:06 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:07 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:07 - root - INFO - Search attempt 16/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]))
2025-08-05 22:54:07 - root - INFO - Phase 2 (Round 16): Adaptive strategy based on 141 existing results
2025-08-05 22:54:07 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:54:08 - modules.pubmed_client - INFO - Esearch found 263 articles, retrieved 263 PMIDs.
2025-08-05 22:54:08 - root - INFO - Search successful! Found 263 articles.
2025-08-05 22:54:08 - root - INFO - Retrieved 263 PMIDs from search result.
2025-08-05 22:54:08 - root - INFO - Found 0 new PMIDs after excluding already processed ones.
2025-08-05 22:54:08 - root - INFO - No new PMIDs to process in this batch. Continuing to next refinement.
2025-08-05 22:54:08 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]))
2025-08-05 22:54:08 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:09 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:09 - root - INFO - Search attempt 17/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical research"[tiab] OR "observational study"[tiab] OR "cohort study"[tiab] OR "case series"[tiab]))
2025-08-05 22:54:09 - root - INFO - Phase 2 (Round 17): Adaptive strategy based on 141 existing results
2025-08-05 22:54:09 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:54:10 - modules.pubmed_client - INFO - Esearch found 17 articles, retrieved 17 PMIDs.
2025-08-05 22:54:10 - root - INFO - Search successful! Found 17 articles.
2025-08-05 22:54:10 - root - INFO - Retrieved 17 PMIDs from search result.
2025-08-05 22:54:10 - root - INFO - Found 1 new PMIDs after excluding already processed ones.
2025-08-05 22:54:10 - root - INFO - Selecting first 1 PMIDs for detailed fetching.
2025-08-05 22:54:10 - modules.pubmed_client - INFO - Executing Efetch for 1 specific PMIDs using POST request
2025-08-05 22:54:10 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:54:10 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:54:10 - modules.data_processor - INFO - Successfully parsed 1 articles from XML.
2025-08-05 22:54:10 - root - INFO - Successfully fetched and parsed 1 articles from PubMed.
2025-08-05 22:54:10 - root - INFO - Starting concurrent screening of 1 new articles...
2025-08-05 22:54:10 - root - INFO - Screening strategy: Strict PICO (Phase 2, 141 results - sufficient)
2025-08-05 22:54:10 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:10 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:13 - root - INFO - Article 26114491 MATCHED PICO. Extracted details.
2025-08-05 22:54:13 - root - INFO - Successfully saved 406 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:54:13 - root - INFO - Updated master PMID list with 1 new PMIDs.
2025-08-05 22:54:13 - root - INFO - Accumulated 1 new articles. Total so far: 142.
2025-08-05 22:54:13 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:54:13 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:54:13 - modules.data_processor - INFO - Successfully saved 142 articles to pico_results.xlsx
2025-08-05 22:54:13 - root - INFO - Screening finished for this batch. Total matching articles so far: 142
2025-08-05 22:54:13 - root - WARNING - Found only 142 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:54:13 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical research"[tiab] OR "observational study"[tiab] OR "cohort study"[tiab] OR "case series"[tiab]))
2025-08-05 22:54:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:15 - root - INFO - Search attempt 18/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "treatment outcome"[tiab] OR "prognosis"[tiab]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical research"[tiab] OR "observational study"[tiab] OR "cohort study"[tiab] OR "case series"[tiab] OR "longitudinal study"[tiab] OR "prospective study"[tiab] OR "retrospective study"[tiab]))
2025-08-05 22:54:15 - root - INFO - Phase 2 (Round 18): Adaptive strategy based on 142 existing results
2025-08-05 22:54:15 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:54:15 - modules.pubmed_client - INFO - Esearch found 240 articles, retrieved 240 PMIDs.
2025-08-05 22:54:15 - root - INFO - Search successful! Found 240 articles.
2025-08-05 22:54:15 - root - INFO - Retrieved 240 PMIDs from search result.
2025-08-05 22:54:15 - root - INFO - Found 172 new PMIDs after excluding already processed ones.
2025-08-05 22:54:15 - root - INFO - Selecting first 172 PMIDs for detailed fetching.
2025-08-05 22:54:15 - modules.pubmed_client - INFO - Executing Efetch for 172 specific PMIDs using POST request
2025-08-05 22:54:17 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:54:17 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:54:17 - modules.data_processor - INFO - Successfully parsed 172 articles from XML.
2025-08-05 22:54:17 - root - INFO - Successfully fetched and parsed 172 articles from PubMed.
2025-08-05 22:54:17 - root - INFO - Starting concurrent screening of 172 new articles...
2025-08-05 22:54:17 - root - INFO - Screening strategy: Strict PICO (Phase 2, 142 results - sufficient)
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:17 - root - INFO - Article 38427135 did not match PICO standard. Skipping.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:17 - root - INFO - Article 38670262 did not match PICO standard. Skipping.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:17 - root - INFO - Article 39027974 did not match PICO standard. Skipping.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:17 - root - INFO - Article 39601967 did not match PICO standard. Skipping.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:17 - root - INFO - Article 40512487 did not match PICO standard. Skipping.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:17 - root - INFO - Article 38722644 did not match PICO standard. Skipping.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:17 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:17 - root - INFO - Article 37598860 did not match PICO standard. Skipping.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:17 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:18 - root - INFO - Article 37031899 did not match PICO standard. Skipping.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:18 - root - INFO - Article 35772694 did not match PICO standard. Skipping.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:18 - root - INFO - Article 35192790 did not match PICO standard. Skipping.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:18 - root - INFO - Article 34519966 did not match PICO standard. Skipping.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:18 - root - INFO - Article 34400806 did not match PICO standard. Skipping.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:18 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 40628205 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 39706129 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 39116541 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 34222291 did not match PICO standard. Skipping.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 40020685 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 37231906 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 39692064 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 34170959 did not match PICO standard. Skipping.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 35976222 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 39227682 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 33710471 did not match PICO standard. Skipping.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 39875573 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 33513185 did not match PICO standard. Skipping.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 38719131 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:19 - root - INFO - Article 39603315 MATCHED PICO. Extracted details.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 32201374 did not match PICO standard. Skipping.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 38134207 MATCHED PICO. Extracted details.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 36657167 MATCHED PICO. Extracted details.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 37094835 MATCHED PICO. Extracted details.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 36108790 MATCHED PICO. Extracted details.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 31907843 did not match PICO standard. Skipping.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 31687624 did not match PICO standard. Skipping.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 34308667 MATCHED PICO. Extracted details.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 31505993 did not match PICO standard. Skipping.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 37088447 MATCHED PICO. Extracted details.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 34613357 MATCHED PICO. Extracted details.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 31055893 did not match PICO standard. Skipping.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:20 - root - INFO - Article 31050721 did not match PICO standard. Skipping.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:21 - root - INFO - Article 37169078 MATCHED PICO. Extracted details.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:21 - root - INFO - Article 37064958 MATCHED PICO. Extracted details.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:21 - root - INFO - Article 33610834 MATCHED PICO. Extracted details.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:21 - root - INFO - Article 33651204 MATCHED PICO. Extracted details.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:21 - root - INFO - Article 32913279 MATCHED PICO. Extracted details.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:21 - root - INFO - Article 33727575 MATCHED PICO. Extracted details.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 33781106 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 36246010 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 28964580 did not match PICO standard. Skipping.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 28902330 did not match PICO standard. Skipping.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 30198967 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 28726777 did not match PICO standard. Skipping.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 31786135 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 32229515 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 31369033 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 31802046 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 32369500 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 28089680 did not match PICO standard. Skipping.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 28057649 did not match PICO standard. Skipping.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 31564356 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 31047436 did not match PICO standard. Skipping.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:22 - root - INFO - Article 30865653 MATCHED PICO. Extracted details.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:23 - root - INFO - Article 27870798 did not match PICO standard. Skipping.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:23 - root - INFO - Article 30657855 MATCHED PICO. Extracted details.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:23 - root - INFO - Article 27465105 did not match PICO standard. Skipping.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:23 - root - INFO - Article 27448830 did not match PICO standard. Skipping.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:23 - root - INFO - Article 31047548 MATCHED PICO. Extracted details.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:23 - root - INFO - Article 27253760 did not match PICO standard. Skipping.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:23 - modules.gemini_client - ERROR - An error occurred while communicating with the Gemini API: 500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/pubmed_meta_search/modules/gemini_client.py", line 65, in _generate_content
    response = model.generate_content(prompt, safety_settings=safety_settings)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/generativeai/generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/client.py", line 835, in generate_content
    response = rpc(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InternalServerError: 500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting
2025-08-05 22:54:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:23 - root - INFO - Article 31014758 MATCHED PICO. Extracted details.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:23 - root - INFO - Article 26524704 did not match PICO standard. Skipping.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 28520639 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - root - INFO - Article 28993010 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 28270492 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 28385474 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 28419398 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 26024102 did not match PICO standard. Skipping.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 29938247 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 33024885 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 28492434 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 25685325 did not match PICO standard. Skipping.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 28456420 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 28620652 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:24 - root - INFO - Article 28084038 MATCHED PICO. Extracted details.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:25 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:25 - root - INFO - Article 25458728 did not match PICO standard. Skipping.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:25 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:25 - root - INFO - Article 25450174 did not match PICO standard. Skipping.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:25 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:25 - root - INFO - Article 27793356 MATCHED PICO. Extracted details.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:25 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:25 - root - INFO - Article 27084000 MATCHED PICO. Extracted details.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:25 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:25 - root - INFO - Article 27366051 MATCHED PICO. Extracted details.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:25 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:25 - root - INFO - Article 28079756 MATCHED PICO. Extracted details.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:25 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 27064389 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 24505261 did not match PICO standard. Skipping.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 24407824 did not match PICO standard. Skipping.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 26865799 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 27627752 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 24061828 did not match PICO standard. Skipping.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 25393287 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 23218698 did not match PICO standard. Skipping.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 25832909 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 23620431 did not match PICO standard. Skipping.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 25709063 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 22850219 did not match PICO standard. Skipping.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 26164826 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 22718153 did not match PICO standard. Skipping.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 22512987 did not match PICO standard. Skipping.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 25881514 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 26298717 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 21325872 did not match PICO standard. Skipping.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:26 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:26 - root - INFO - Article 25787216 MATCHED PICO. Extracted details.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:26 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 24771185 MATCHED PICO. Extracted details.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 25646033 MATCHED PICO. Extracted details.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 20887243 did not match PICO standard. Skipping.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 20683378 did not match PICO standard. Skipping.
2025-08-05 22:54:27 - root - INFO - Article 20636487 did not match PICO standard. Skipping.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 25769245 MATCHED PICO. Extracted details.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 25190663 MATCHED PICO. Extracted details.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 20357194 did not match PICO standard. Skipping.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 25578255 MATCHED PICO. Extracted details.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 24879944 MATCHED PICO. Extracted details.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:27 - root - INFO - Article 19797228 did not match PICO standard. Skipping.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 21427630 MATCHED PICO. Extracted details.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 23696601 MATCHED PICO. Extracted details.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 22466459 MATCHED PICO. Extracted details.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 19243827 did not match PICO standard. Skipping.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 18628724 did not match PICO standard. Skipping.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 18951425 did not match PICO standard. Skipping.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 18925410 did not match PICO standard. Skipping.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 18502512 did not match PICO standard. Skipping.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 24618706 MATCHED PICO. Extracted details.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 18369068 did not match PICO standard. Skipping.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 23137630 MATCHED PICO. Extracted details.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 18235025 did not match PICO standard. Skipping.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 20920825 MATCHED PICO. Extracted details.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 17698019 did not match PICO standard. Skipping.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 22027699 MATCHED PICO. Extracted details.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 22826551 MATCHED PICO. Extracted details.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 17562066 did not match PICO standard. Skipping.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:28 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:28 - root - INFO - Article 21701525 MATCHED PICO. Extracted details.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:28 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 21496928 MATCHED PICO. Extracted details.
2025-08-05 22:54:29 - root - INFO - Article 19899970 MATCHED PICO. Extracted details.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 21056515 MATCHED PICO. Extracted details.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 16986086 did not match PICO standard. Skipping.
2025-08-05 22:54:29 - root - INFO - Article 21494878 MATCHED PICO. Extracted details.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 19643488 MATCHED PICO. Extracted details.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 19692384 MATCHED PICO. Extracted details.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - ERROR - An error occurred while communicating with the Gemini API: 500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/pubmed_meta_search/modules/gemini_client.py", line 65, in _generate_content
    response = model.generate_content(prompt, safety_settings=safety_settings)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/generativeai/generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/client.py", line 835, in generate_content
    response = rpc(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/retry/retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/retry/retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/retry/retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/retry/retry_unary.py", line 147, in retry_target
    result = target()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InternalServerError: 500 An internal error has occurred. Please retry or report in https://developers.generativeai.google/guide/troubleshooting
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 20610478 MATCHED PICO. Extracted details.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 12565808 did not match PICO standard. Skipping.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 15531316 did not match PICO standard. Skipping.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 12153801 did not match PICO standard. Skipping.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:29 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:29 - root - INFO - Article 18412594 MATCHED PICO. Extracted details.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:29 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 11030816 did not match PICO standard. Skipping.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 18239678 MATCHED PICO. Extracted details.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 18303233 MATCHED PICO. Extracted details.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 20163861 MATCHED PICO. Extracted details.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 11011682 did not match PICO standard. Skipping.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 20169357 MATCHED PICO. Extracted details.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 10442900 did not match PICO standard. Skipping.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 10713926 did not match PICO standard. Skipping.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 17157598 MATCHED PICO. Extracted details.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 7534060 did not match PICO standard. Skipping.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 17458796 MATCHED PICO. Extracted details.
2025-08-05 22:54:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:30 - root - INFO - Article 17558313 MATCHED PICO. Extracted details.
2025-08-05 22:54:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:31 - root - INFO - Article 17653552 MATCHED PICO. Extracted details.
2025-08-05 22:54:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:31 - root - INFO - Article 16325706 MATCHED PICO. Extracted details.
2025-08-05 22:54:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:31 - root - INFO - Article 17303061 MATCHED PICO. Extracted details.
2025-08-05 22:54:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:31 - root - INFO - Article 17324698 MATCHED PICO. Extracted details.
2025-08-05 22:54:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:31 - root - INFO - Article 17599415 MATCHED PICO. Extracted details.
2025-08-05 22:54:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:31 - root - INFO - Article 12383814 MATCHED PICO. Extracted details.
2025-08-05 22:54:31 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:31 - root - INFO - Article 16525824 MATCHED PICO. Extracted details.
2025-08-05 22:54:32 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:32 - root - INFO - Article 11944854 MATCHED PICO. Extracted details.
2025-08-05 22:54:32 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:32 - root - INFO - Article 15505069 MATCHED PICO. Extracted details.
2025-08-05 22:54:32 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:32 - root - INFO - Article 11917797 MATCHED PICO. Extracted details.
2025-08-05 22:54:33 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:36 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:36 - root - INFO - Article 31047325 MATCHED PICO. Extracted details.
2025-08-05 22:54:36 - root - INFO - Successfully saved 578 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:54:36 - root - INFO - Updated master PMID list with 172 new PMIDs.
2025-08-05 22:54:36 - root - INFO - Accumulated 103 new articles. Total so far: 245.
2025-08-05 22:54:36 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:54:36 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:54:36 - modules.data_processor - INFO - Successfully saved 245 articles to pico_results.xlsx
2025-08-05 22:54:36 - root - INFO - Screening finished for this batch. Total matching articles so far: 245
2025-08-05 22:54:36 - root - WARNING - Found only 245 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:54:36 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "clinical outcome"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "wound healing"[tiab] OR "disease progression"[tiab] OR "clinical course"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "treatment outcome"[tiab] OR "prognosis"[tiab]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical research"[tiab] OR "observational study"[tiab] OR "cohort study"[tiab] OR "case series"[tiab] OR "longitudinal study"[tiab] OR "prospective study"[tiab] OR "retrospective study"[tiab]))
2025-08-05 22:54:36 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:38 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:38 - root - INFO - Search attempt 19/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab] OR "photoreceptors"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical studies"[tiab] OR "observational study"[tiab] OR "cohort study"[tiab] OR "case series"[tiab] OR "longitudinal study"[tiab] OR "prospective study"[tiab] OR "retrospective study"[tiab] OR "clinical research"[mesh] OR "observational study"[mesh]))
2025-08-05 22:54:38 - root - INFO - Phase 2 (Round 19): Adaptive strategy based on 245 existing results
2025-08-05 22:54:38 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:54:38 - modules.pubmed_client - INFO - Esearch found 320 articles, retrieved 320 PMIDs.
2025-08-05 22:54:38 - root - INFO - Search successful! Found 320 articles.
2025-08-05 22:54:38 - root - INFO - Retrieved 320 PMIDs from search result.
2025-08-05 22:54:38 - root - INFO - Found 80 new PMIDs after excluding already processed ones.
2025-08-05 22:54:38 - root - INFO - Selecting first 80 PMIDs for detailed fetching.
2025-08-05 22:54:38 - modules.pubmed_client - INFO - Executing Efetch for 80 specific PMIDs using POST request
2025-08-05 22:54:40 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:54:40 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:54:40 - modules.data_processor - INFO - Successfully parsed 80 articles from XML.
2025-08-05 22:54:40 - root - INFO - Successfully fetched and parsed 80 articles from PubMed.
2025-08-05 22:54:40 - root - INFO - Starting concurrent screening of 80 new articles...
2025-08-05 22:54:40 - root - INFO - Screening strategy: Strict PICO (Phase 2, 245 results - sufficient)
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 36249700 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 38027818 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 40434460 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 39758131 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 36249695 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 37786347 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 38552931 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 38284102 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 39033924 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 37459045 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:40 - root - INFO - Article 32002591 did not match PICO standard. Skipping.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:41 - root - INFO - Article 31694837 did not match PICO standard. Skipping.
2025-08-05 22:54:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:41 - root - INFO - Article 31659814 did not match PICO standard. Skipping.
2025-08-05 22:54:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:41 - root - INFO - Article 31174669 did not match PICO standard. Skipping.
2025-08-05 22:54:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:41 - root - INFO - Article 38809490 MATCHED PICO. Extracted details.
2025-08-05 22:54:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 38698112 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 30419340 did not match PICO standard. Skipping.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 36162382 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 40502292 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 36249678 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 36038720 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 29625839 did not match PICO standard. Skipping.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 40553257 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 36531577 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 29160784 did not match PICO standard. Skipping.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 29110945 did not match PICO standard. Skipping.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 39793657 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 33036197 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 34070774 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 28412773 did not match PICO standard. Skipping.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 28655307 did not match PICO standard. Skipping.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 38160882 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 28878022 did not match PICO standard. Skipping.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:42 - root - INFO - Article 36345883 MATCHED PICO. Extracted details.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 28111323 did not match PICO standard. Skipping.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 32387055 MATCHED PICO. Extracted details.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 28109563 did not match PICO standard. Skipping.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 33600134 MATCHED PICO. Extracted details.
2025-08-05 22:54:43 - root - INFO - Article 26077580 did not match PICO standard. Skipping.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 33331859 MATCHED PICO. Extracted details.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 32107066 MATCHED PICO. Extracted details.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 33640493 MATCHED PICO. Extracted details.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 24983793 did not match PICO standard. Skipping.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 34310095 MATCHED PICO. Extracted details.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 22496012 did not match PICO standard. Skipping.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 23891523 did not match PICO standard. Skipping.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 21693600 did not match PICO standard. Skipping.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:43 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:43 - root - INFO - Article 21093923 did not match PICO standard. Skipping.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:43 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 29929478 MATCHED PICO. Extracted details.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 27478353 MATCHED PICO. Extracted details.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 30681643 MATCHED PICO. Extracted details.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 29499174 MATCHED PICO. Extracted details.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 19911018 did not match PICO standard. Skipping.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 27566855 MATCHED PICO. Extracted details.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 19604160 did not match PICO standard. Skipping.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 19232732 did not match PICO standard. Skipping.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 18642209 did not match PICO standard. Skipping.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:44 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:44 - root - INFO - Article 18547537 did not match PICO standard. Skipping.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:44 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 25103650 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 29190241 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 29135888 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 15808248 did not match PICO standard. Skipping.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 29160779 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - root - INFO - Article 15300389 did not match PICO standard. Skipping.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 9487758 did not match PICO standard. Skipping.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 25315664 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 28368974 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 27791249 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 31047298 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 20537310 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 23874084 MATCHED PICO. Extracted details.
2025-08-05 22:54:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:45 - root - INFO - Article 24675391 MATCHED PICO. Extracted details.
2025-08-05 22:54:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:46 - root - INFO - Article 21907969 MATCHED PICO. Extracted details.
2025-08-05 22:54:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:46 - root - INFO - Article 19174716 MATCHED PICO. Extracted details.
2025-08-05 22:54:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:46 - root - INFO - Article 21872935 MATCHED PICO. Extracted details.
2025-08-05 22:54:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:46 - root - INFO - Article 19815280 MATCHED PICO. Extracted details.
2025-08-05 22:54:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:46 - root - INFO - Article 19952998 MATCHED PICO. Extracted details.
2025-08-05 22:54:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:46 - root - INFO - Article 19404661 MATCHED PICO. Extracted details.
2025-08-05 22:54:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:47 - root - INFO - Article 23433790 MATCHED PICO. Extracted details.
2025-08-05 22:54:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:47 - root - INFO - Article 17891059 MATCHED PICO. Extracted details.
2025-08-05 22:54:47 - root - INFO - Successfully saved 658 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:54:47 - root - INFO - Updated master PMID list with 80 new PMIDs.
2025-08-05 22:54:47 - root - INFO - Accumulated 43 new articles. Total so far: 288.
2025-08-05 22:54:47 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:54:47 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:54:47 - modules.data_processor - INFO - Successfully saved 288 articles to pico_results.xlsx
2025-08-05 22:54:48 - root - INFO - Screening finished for this batch. Total matching articles so far: 288
2025-08-05 22:54:48 - root - WARNING - Found only 288 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:54:48 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab] OR "photoreceptors"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "clinical studies"[tiab] OR "observational study"[tiab] OR "cohort study"[tiab] OR "case series"[tiab] OR "longitudinal study"[tiab] OR "prospective study"[tiab] OR "retrospective study"[tiab] OR "clinical research"[mesh] OR "observational study"[mesh]))
2025-08-05 22:54:48 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:49 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:49 - root - INFO - Search attempt 20/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab] OR "photoreceptors"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "observational study"[mesh] OR "cohort study"[mesh] OR "case series"[mesh] OR "longitudinal study"[mesh] OR "prospective study"[mesh] OR "retrospective study"[mesh] OR "clinical research"[mesh] OR "clinical study"[mesh]))
2025-08-05 22:54:49 - root - INFO - Phase 2 (Round 20): Adaptive strategy based on 288 existing results
2025-08-05 22:54:49 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:54:50 - modules.pubmed_client - INFO - Esearch found 80 articles, retrieved 80 PMIDs.
2025-08-05 22:54:50 - root - INFO - Search successful! Found 80 articles.
2025-08-05 22:54:50 - root - INFO - Retrieved 80 PMIDs from search result.
2025-08-05 22:54:50 - root - INFO - Found 0 new PMIDs after excluding already processed ones.
2025-08-05 22:54:50 - root - INFO - No new PMIDs to process in this batch. Continuing to next refinement.
2025-08-05 22:54:50 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab] OR "photoreceptors"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab]) AND ("clinical trial"[pt] OR "randomized controlled trial"[pt] OR "controlled clinical trial"[pt] OR "observational study"[mesh] OR "cohort study"[mesh] OR "case series"[mesh] OR "longitudinal study"[mesh] OR "prospective study"[mesh] OR "retrospective study"[mesh] OR "clinical research"[mesh] OR "clinical study"[mesh]))
2025-08-05 22:54:50 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:51 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:51 - root - INFO - Search attempt 21/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab] OR "photoreceptors"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh] OR "histopathology"[tiab]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab] OR "clinical setting"[tiab]) AND ("observational study"[mesh] OR "cohort study"[mesh] OR "case series"[mesh] OR "longitudinal study"[mesh] OR "prospective study"[mesh] OR "retrospective study"[mesh] OR "clinical research"[mesh] OR "clinical study"[mesh] OR "follow-up studies"[mesh] OR "natural history studies"[mesh] OR "clinical outcome"[tiab] OR "patient outcomes"[tiab] OR "disease course"[tiab] OR "disease progression"[tiab] OR "clinical observations"[tiab]))
2025-08-05 22:54:51 - root - INFO - Phase 2 (Round 21): Adaptive strategy based on 288 existing results
2025-08-05 22:54:51 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:54:52 - modules.pubmed_client - INFO - Esearch found 303 articles, retrieved 303 PMIDs.
2025-08-05 22:54:52 - root - INFO - Search successful! Found 303 articles.
2025-08-05 22:54:52 - root - INFO - Retrieved 303 PMIDs from search result.
2025-08-05 22:54:52 - root - INFO - Found 156 new PMIDs after excluding already processed ones.
2025-08-05 22:54:52 - root - INFO - Selecting first 156 PMIDs for detailed fetching.
2025-08-05 22:54:52 - modules.pubmed_client - INFO - Executing Efetch for 156 specific PMIDs using POST request
2025-08-05 22:54:54 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:54:54 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:54:54 - modules.data_processor - INFO - Successfully parsed 156 articles from XML.
2025-08-05 22:54:54 - root - INFO - Successfully fetched and parsed 156 articles from PubMed.
2025-08-05 22:54:54 - root - INFO - Starting concurrent screening of 156 new articles...
2025-08-05 22:54:54 - root - INFO - Screening strategy: Strict PICO (Phase 2, 288 results - sufficient)
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 39930219 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 40682461 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 39689291 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 39684332 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 39365260 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 39172949 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 40221802 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 40330967 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 39620831 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 38598664 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 38786570 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 38687492 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 39913124 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 37772657 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - root - INFO - Article 38981710 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 37517799 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 38349778 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 36755326 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - root - INFO - Article 37254103 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 36950236 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 36032584 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - root - INFO - Article 38300334 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 36526863 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 37660150 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 35836149 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 34767301 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - root - INFO - Article 36425633 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 35196199 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 34448806 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 34491262 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 34282240 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 33681224 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 34001980 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - root - INFO - Article 34058271 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - root - INFO - Article 33430331 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 33949280 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:54 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:54 - root - INFO - Article 34003988 did not match PICO standard. Skipping.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:54 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 33738626 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 32710488 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 32525594 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 33130003 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - root - INFO - Article 33348085 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 32434914 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 32282662 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 32604342 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 32160961 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 32269061 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 33145006 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 31753808 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 31323680 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 30980814 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 30404862 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - root - INFO - Article 30907074 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - root - INFO - Article 30739141 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 31146784 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 30383198 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 30444724 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 31121590 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 30357336 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - root - INFO - Article 30355956 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 30304737 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 30122442 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 30098171 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 29558533 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 29752877 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 29946065 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 28829422 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 30284413 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 29517581 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 28731053 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 28554398 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - root - INFO - Article 29781974 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 29097185 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 29324593 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 28437524 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 28288486 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 28195603 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - root - INFO - Article 27787445 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 28092344 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 27657855 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 27701734 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 27409464 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 27145251 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 27496526 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 27199657 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 27082298 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 26394020 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 26985795 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:55 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:55 - root - INFO - Article 26079475 did not match PICO standard. Skipping.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:55 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 26067391 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - root - INFO - Article 26176960 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 27262765 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 27895380 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 25301882 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 25869002 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 26024124 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 24624338 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 23744996 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 25198169 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 23584692 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 23410728 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 22661465 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 23538573 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 22878106 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 24026873 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 22508168 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 23452406 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 21743006 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 23510045 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 21725716 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - root - INFO - Article 21733363 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 40443830 MATCHED PICO. Extracted details.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 21410906 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 21157398 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 20517175 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 39540601 MATCHED PICO. Extracted details.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 20346087 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 20176401 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 20024687 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 19202424 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 19507101 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 19197318 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 19171212 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 19692368 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 19117919 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 18401795 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 19060287 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 19277221 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 17672091 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 17122141 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 17239336 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 16723482 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:56 - root - INFO - Article 15316735 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - root - INFO - Article 12234886 did not match PICO standard. Skipping.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:56 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 14714187 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 12430038 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 11274085 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 16183127 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 9951642 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 9274413 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 11453866 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 9085110 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 10071156 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 32761460 MATCHED PICO. Extracted details.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 9112188 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 9703720 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 1630786 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 2419817 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 2089541 did not match PICO standard. Skipping.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 27409485 MATCHED PICO. Extracted details.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 32488329 MATCHED PICO. Extracted details.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 31248784 MATCHED PICO. Extracted details.
2025-08-05 22:54:57 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:57 - root - INFO - Article 30470956 MATCHED PICO. Extracted details.
2025-08-05 22:54:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:58 - root - INFO - Article 27913869 MATCHED PICO. Extracted details.
2025-08-05 22:54:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:58 - root - INFO - Article 19327745 MATCHED PICO. Extracted details.
2025-08-05 22:54:58 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:54:58 - root - INFO - Article 22576370 MATCHED PICO. Extracted details.
2025-08-05 22:54:58 - root - INFO - Successfully saved 814 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:54:58 - root - INFO - Updated master PMID list with 156 new PMIDs.
2025-08-05 22:54:58 - root - INFO - Accumulated 10 new articles. Total so far: 298.
2025-08-05 22:54:58 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:54:58 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:54:58 - modules.data_processor - INFO - Successfully saved 298 articles to pico_results.xlsx
2025-08-05 22:54:58 - root - INFO - Screening finished for this batch. Total matching articles so far: 298
2025-08-05 22:54:58 - root - WARNING - Found only 298 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:54:58 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab] OR "photoreceptors"[mesh]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh] OR "histopathology"[tiab]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab] OR "clinical setting"[tiab]) AND ("observational study"[mesh] OR "cohort study"[mesh] OR "case series"[mesh] OR "longitudinal study"[mesh] OR "prospective study"[mesh] OR "retrospective study"[mesh] OR "clinical research"[mesh] OR "clinical study"[mesh] OR "follow-up studies"[mesh] OR "natural history studies"[mesh] OR "clinical outcome"[tiab] OR "patient outcomes"[tiab] OR "disease course"[tiab] OR "disease progression"[tiab] OR "clinical observations"[tiab]))
2025-08-05 22:54:58 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:00 - root - INFO - Search attempt 22/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptors"[mesh] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh] OR "histopathology"[tiab] OR "disease course"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab] OR "clinical setting"[tiab]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "longitudinal study"[Publication Type] OR "prospective study"[Publication Type] OR "retrospective study"[Publication Type] OR "clinical research"[mesh] OR "clinical study"[mesh] OR "follow-up studies"[mesh] OR "natural history studies"[mesh] OR "clinical outcome"[tiab] OR "patient outcomes"[tiab] OR "disease course"[tiab] OR "disease progression"[tiab] OR "clinical observations"[tiab]))
2025-08-05 22:55:00 - root - INFO - Phase 2 (Round 22): Adaptive strategy based on 298 existing results
2025-08-05 22:55:00 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:55:01 - modules.pubmed_client - INFO - Esearch found 375 articles, retrieved 375 PMIDs.
2025-08-05 22:55:01 - root - INFO - Search successful! Found 375 articles.
2025-08-05 22:55:01 - root - INFO - Retrieved 375 PMIDs from search result.
2025-08-05 22:55:01 - root - INFO - Found 8 new PMIDs after excluding already processed ones.
2025-08-05 22:55:01 - root - INFO - Selecting first 8 PMIDs for detailed fetching.
2025-08-05 22:55:01 - modules.pubmed_client - INFO - Executing Efetch for 8 specific PMIDs using POST request
2025-08-05 22:55:02 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 22:55:02 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 22:55:02 - modules.data_processor - INFO - Successfully parsed 8 articles from XML.
2025-08-05 22:55:02 - root - INFO - Successfully fetched and parsed 8 articles from PubMed.
2025-08-05 22:55:02 - root - INFO - Starting concurrent screening of 8 new articles...
2025-08-05 22:55:02 - root - INFO - Screening strategy: Strict PICO (Phase 2, 298 results - sufficient)
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:02 - root - INFO - Article 23736827 did not match PICO standard. Skipping.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:02 - root - INFO - Article 33506710 did not match PICO standard. Skipping.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:02 - root - INFO - Article 33531591 did not match PICO standard. Skipping.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:02 - root - INFO - Article 27533772 did not match PICO standard. Skipping.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:02 - root - INFO - Article 24474382 did not match PICO standard. Skipping.
2025-08-05 22:55:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:02 - root - INFO - Article 32067383 did not match PICO standard. Skipping.
2025-08-05 22:55:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:03 - root - INFO - Article 38438003 MATCHED PICO. Extracted details.
2025-08-05 22:55:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:03 - root - INFO - Article 32091488 MATCHED PICO. Extracted details.
2025-08-05 22:55:03 - root - INFO - Successfully saved 822 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 22:55:03 - root - INFO - Updated master PMID list with 8 new PMIDs.
2025-08-05 22:55:03 - root - INFO - Accumulated 2 new articles. Total so far: 300.
2025-08-05 22:55:03 - modules.data_processor - INFO - Successfully loaded 37867 journal entries into dictionary.
2025-08-05 22:55:04 - modules.data_processor - INFO - Writing formatted data to Excel file: pico_results.xlsx
2025-08-05 22:55:04 - modules.data_processor - INFO - Successfully saved 300 articles to pico_results.xlsx
2025-08-05 22:55:04 - root - INFO - Screening finished for this batch. Total matching articles so far: 300
2025-08-05 22:55:04 - root - WARNING - Found only 300 studies, which is below the minimum of 500. Refining PubMed query for next attempt...
2025-08-05 22:55:04 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptors"[mesh] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh] OR "histopathology"[tiab] OR "disease course"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab] OR "clinical setting"[tiab]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "longitudinal study"[Publication Type] OR "prospective study"[Publication Type] OR "retrospective study"[Publication Type] OR "clinical research"[mesh] OR "clinical study"[mesh] OR "follow-up studies"[mesh] OR "natural history studies"[mesh] OR "clinical outcome"[tiab] OR "patient outcomes"[tiab] OR "disease course"[tiab] OR "disease progression"[tiab] OR "clinical observations"[tiab]))
2025-08-05 22:55:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:06 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:06 - root - INFO - Search attempt 23/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptors"[mesh] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh] OR "histopathology"[tiab] OR "disease course"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab] OR "clinical setting"[tiab]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "longitudinal study"[Publication Type] OR "prospective study"[Publication Type] OR "retrospective study"[Publication Type] OR "clinical research"[mesh] OR "clinical study"[mesh] OR "follow-up studies"[mesh] OR "natural history studies"[mesh] OR "clinical outcome"[tiab] OR "patient outcomes"[tiab] OR "disease course"[tiab] OR "disease progression"[tiab] OR "clinical observations"[tiab]) NOT ("pathway"[tiab] OR "molecular"[tiab] OR "signaling"[tiab] OR "biological process"[mesh] OR "molecular mechanism"[tiab] OR "cellular process"[tiab] OR "biochemical pathway"[tiab] OR "molecular pathway"[tiab] OR "pathogenesis"[mesh] OR "molecular mechanisms"[tiab] OR "cellular mechanisms"[tiab] OR "biochemical mechanisms"[tiab]))
2025-08-05 22:55:06 - root - INFO - Phase 2 (Round 23): Adaptive strategy based on 300 existing results
2025-08-05 22:55:06 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:55:07 - modules.pubmed_client - INFO - Esearch found 355 articles, retrieved 355 PMIDs.
2025-08-05 22:55:07 - root - INFO - Search successful! Found 355 articles.
2025-08-05 22:55:07 - root - INFO - Retrieved 355 PMIDs from search result.
2025-08-05 22:55:07 - root - INFO - Found 0 new PMIDs after excluding already processed ones.
2025-08-05 22:55:07 - root - INFO - No new PMIDs to process in this batch. Continuing to next refinement.
2025-08-05 22:55:07 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptors"[mesh] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab] OR "retinal pigment epithelium"[MeSH Terms] OR "RPE"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh] OR "histopathology"[tiab] OR "disease course"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab] OR "clinical setting"[tiab]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "longitudinal study"[Publication Type] OR "prospective study"[Publication Type] OR "retrospective study"[Publication Type] OR "clinical research"[mesh] OR "clinical study"[mesh] OR "follow-up studies"[mesh] OR "natural history studies"[mesh] OR "clinical outcome"[tiab] OR "patient outcomes"[tiab] OR "disease course"[tiab] OR "disease progression"[tiab] OR "clinical observations"[tiab]) NOT ("pathway"[tiab] OR "molecular"[tiab] OR "signaling"[tiab] OR "biological process"[mesh] OR "molecular mechanism"[tiab] OR "cellular process"[tiab] OR "biochemical pathway"[tiab] OR "molecular pathway"[tiab] OR "pathogenesis"[mesh] OR "molecular mechanisms"[tiab] OR "cellular mechanisms"[tiab] OR "biochemical mechanisms"[tiab]))
2025-08-05 22:55:07 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 22:55:09 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 22:55:09 - root - INFO - Search attempt 24/100 with query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptors"[mesh] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh] OR "histopathology"[tiab] OR "disease course"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab] OR "clinical setting"[tiab]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "longitudinal study"[Publication Type] OR "prospective study"[Publication Type] OR "retrospective study"[Publication Type] OR "clinical research"[mesh] OR "clinical study"[mesh] OR "follow-up studies"[mesh] OR "natural history studies"[mesh] OR "clinical outcome"[tiab] OR "patient outcomes"[tiab] OR "disease course"[tiab] OR "disease progression"[tiab] OR "clinical observations"[tiab]) NOT ("pathway"[tiab] OR "molecular"[tiab] OR "signaling"[tiab] OR "biological process"[mesh] OR "molecular mechanism"[tiab] OR "cellular process"[tiab] OR "biochemical pathway"[tiab] OR "molecular pathway"[tiab] OR "pathogenesis"[mesh] OR "molecular mechanisms"[tiab] OR "cellular mechanisms"[tiab] OR "biochemical mechanisms"[tiab] OR "mechanism"[tiab] OR "pathways"[mesh]))
2025-08-05 22:55:09 - root - INFO - Phase 2 (Round 24): Adaptive strategy based on 300 existing results
2025-08-05 22:55:09 - modules.pubmed_client - INFO - Executing Esearch for term: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] ... (retmax=100000)
2025-08-05 22:55:09 - modules.pubmed_client - INFO - Esearch found 37 articles, retrieved 37 PMIDs.
2025-08-05 22:55:09 - root - INFO - Search successful! Found 37 articles.
2025-08-05 22:55:09 - root - INFO - Retrieved 37 PMIDs from search result.
2025-08-05 22:55:09 - root - INFO - Found 0 new PMIDs after excluding already processed ones.
2025-08-05 22:55:09 - root - INFO - No new PMIDs to process in this batch. Continuing to next refinement.
2025-08-05 22:55:09 - modules.gemini_client - WARNING - Refining failed PubMed query: (("age-related macular degeneration"[MeSH Terms] OR "macular degeneration, age-related"[MeSH Terms] OR "AMD"[tiab] OR "nAMD"[tiab] OR "age related macular degeneration"[tiab] OR "age-related maculopathy"[tiab] OR "geographic atrophy"[tiab] OR "GA"[tiab]) AND ("photoreceptor"[MeSH Terms] OR "photoreceptor cell"[tiab] OR "cone cell"[MeSH Terms] OR "rod cell"[MeSH Terms] OR "photoreceptor degeneration"[tiab] OR "photoreceptor damage"[tiab] OR "photoreceptor loss"[tiab] OR "photoreceptor repair"[tiab] OR "photoreceptor regeneration"[tiab] OR "photoreceptor dysfunction"[tiab] OR "photoreceptor cells"[tiab] OR "photoreceptors"[mesh] OR "photoreceptor damage"[mesh] OR "photoreceptor regeneration"[mesh] OR "photoreceptor restoration"[tiab] OR "outer segment"[tiab]) AND ("progression"[tiab] OR "course"[tiab] OR "deterioration"[tiab] OR "natural history"[tiab] OR "damage"[tiab] OR "repair"[tiab] OR "regeneration"[tiab] OR "restoration"[tiab] OR "recovery"[tiab] OR "lesion"[tiab] OR "loss"[tiab] OR "pathology"[tiab] OR "disease progression"[tiab] OR "structural damage"[tiab] OR "functional recovery"[tiab] OR "vision restoration"[tiab] OR "visual function"[tiab] OR "outcome"[tiab] OR "prognosis"[tiab] OR "lesions"[mesh] OR "damage"[mesh] OR "recovery"[mesh] OR "progression"[mesh] OR "histopathology"[tiab] OR "disease course"[mesh]) AND ("human"[MeSH Terms] OR "human"[tiab] OR "patient"[tiab] OR "patients"[tiab] OR "clinical setting"[tiab]) AND ("clinical trial"[Publication Type] OR "randomized controlled trial"[Publication Type] OR "observational study"[Publication Type] OR "cohort study"[Publication Type] OR "case series"[Publication Type] OR "longitudinal study"[Publication Type] OR "prospective study"[Publication Type] OR "retrospective study"[Publication Type] OR "clinical research"[mesh] OR "clinical study"[mesh] OR "follow-up studies"[mesh] OR "natural history studies"[mesh] OR "clinical outcome"[tiab] OR "patient outcomes"[tiab] OR "disease course"[tiab] OR "disease progression"[tiab] OR "clinical observations"[tiab]) NOT ("pathway"[tiab] OR "molecular"[tiab] OR "signaling"[tiab] OR "biological process"[mesh] OR "molecular mechanism"[tiab] OR "cellular process"[tiab] OR "biochemical pathway"[tiab] OR "molecular pathway"[tiab] OR "pathogenesis"[mesh] OR "molecular mechanisms"[tiab] OR "cellular mechanisms"[tiab] OR "biochemical mechanisms"[tiab] OR "mechanism"[tiab] OR "pathways"[mesh]))
2025-08-05 22:55:09 - modules.gemini_client - INFO - Sending prompt to Gemini API...
