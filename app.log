2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760363 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760360 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - root - INFO - Article 40760463 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - INFO - Article 40760507 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760448 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760172 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - WARNING - Skipping article 40759961 due to missing abstract.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760434 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760210 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760162 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760152 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760151 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760147 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - INFO - Article 40760128 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - INFO - Article 40760145 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760095 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760029 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - WARNING - Skipping article 40759782 due to missing abstract.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760060 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760058 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - INFO - Article 40759939 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760075 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759984 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759966 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - root - INFO - Article 40760002 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40760012 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759983 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759998 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759888 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759876 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759908 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759899 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759872 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759895 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759768 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759829 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759806 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759788 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759717 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759735 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759710 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759617 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - INFO - Article 40759702 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759606 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759692 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759629 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - root - INFO - Article 40759647 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759553 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759609 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759526 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759517 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759519 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759466 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759422 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759436 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - WARNING - Skipping article 40759174 due to missing abstract.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759457 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759424 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - INFO - Article 40759407 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759349 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759361 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759404 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759355 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - INFO - Article 40759339 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759403 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759278 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759276 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759391 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759287 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759211 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759241 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - root - INFO - Article 40759300 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - root - INFO - Article 40759223 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:19:59 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:19:59 - root - INFO - Article 40759267 did not match PICO standard. Skipping.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:19:59 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40759166 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40759180 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40759071 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40759038 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40759059 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40759103 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758979 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40759047 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758983 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758953 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40759087 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758886 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40759036 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758989 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - root - WARNING - Skipping article 40758550 due to missing abstract.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758901 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758865 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758960 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758862 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758847 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758830 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758844 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758759 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - root - WARNING - Skipping article 40758366 due to missing abstract.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758786 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758756 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - root - WARNING - Skipping article 40758330 due to missing abstract.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758752 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - root - INFO - Article 40758757 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758695 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758745 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758738 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758666 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758751 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758546 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758710 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758552 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758421 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758507 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758405 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758386 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758484 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758388 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758334 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758359 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758370 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758319 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758306 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758107 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - root - INFO - Article 40758129 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758227 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758062 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758223 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - root - INFO - Article 40758272 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758014 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758038 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757990 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758005 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - root - INFO - Article 40758013 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40758057 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757993 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757826 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - root - INFO - Article 40757961 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - root - INFO - Article 40757847 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757880 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757923 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757889 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757759 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757718 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757724 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757659 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757627 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757684 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757636 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757642 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757524 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757604 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757475 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - root - INFO - Article 40757599 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:00 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:00 - root - INFO - Article 40757620 did not match PICO standard. Skipping.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:00 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757430 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757466 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757462 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - root - INFO - Article 40757425 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757456 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757465 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757393 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757417 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757413 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757354 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757372 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757296 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757287 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757284 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757321 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757369 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757294 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757239 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757317 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757162 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757155 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - root - INFO - Article 40757222 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757151 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - root - INFO - Article 40757238 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757186 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757111 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757129 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757090 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757070 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757087 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757108 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757058 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757025 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - root - INFO - Article 40757048 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757002 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756951 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - root - INFO - Article 40757014 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40757001 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - root - INFO - Article 40756910 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756868 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - root - INFO - Article 40756996 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756863 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756809 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756899 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756781 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756792 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756762 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756796 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756710 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756687 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756704 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756703 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756679 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756676 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756656 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - root - INFO - Article 40756668 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756638 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756657 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756633 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756649 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756639 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756610 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756625 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - root - INFO - Article 40756561 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756596 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756601 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756597 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756589 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756546 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756548 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756526 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756483 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756545 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756491 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756496 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - root - INFO - Article 40756497 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756485 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756570 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756471 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:01 - root - INFO - Article 40756462 did not match PICO standard. Skipping.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:01 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:01 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756465 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - root - INFO - Article 40756440 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756481 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756423 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756438 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756403 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756401 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756418 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756429 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756414 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756383 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756371 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756390 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756382 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756343 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - root - INFO - Article 40756339 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756317 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756311 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756277 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756252 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756275 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - root - INFO - Article 40756267 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756296 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756246 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756249 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - root - INFO - Article 40756224 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756208 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756216 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756210 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756193 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756203 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756191 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756199 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756196 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - root - INFO - Article 40756162 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756190 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - root - INFO - Article 40756171 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756120 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756115 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756139 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756130 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756215 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756132 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756102 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756166 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756045 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756069 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - root - INFO - Article 40756071 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756052 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - root - INFO - Article 40756072 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756019 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756036 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40756003 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755974 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755953 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - root - INFO - Article 40755923 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755943 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755896 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755951 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755912 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755891 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755879 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755844 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - root - INFO - Article 40755949 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - root - INFO - Article 40755870 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755874 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - root - INFO - Article 40755845 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755840 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755856 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755852 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755860 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755834 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755824 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755832 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755809 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755813 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755808 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755817 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:02 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:02 - root - INFO - Article 40755805 did not match PICO standard. Skipping.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:02 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755793 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755825 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755787 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755775 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755781 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755771 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755795 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755748 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755752 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755742 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755778 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755789 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - root - INFO - Article 40755780 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755711 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755720 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755734 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755726 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755740 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - root - INFO - Article 40755681 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755679 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755680 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755676 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - root - INFO - Article 40755675 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755639 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - root - INFO - Article 40755665 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755637 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755604 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755609 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755611 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755631 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755585 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755589 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755624 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755565 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755580 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755561 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755553 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755562 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755541 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755544 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755537 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755555 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - root - INFO - Article 40755548 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755529 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755546 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755516 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755502 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755517 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755523 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755495 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755458 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755491 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755488 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755498 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755450 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - root - INFO - Article 40755510 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755455 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755503 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755443 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - root - INFO - Article 40755456 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755442 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755449 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755444 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755414 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755428 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - root - INFO - Article 40755408 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755432 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755429 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755348 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755353 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755327 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755393 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755306 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - root - INFO - Article 40755307 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - root - INFO - Article 40755298 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - root - INFO - Article 40755380 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:03 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:03 - root - INFO - Article 40755293 did not match PICO standard. Skipping.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:03 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755399 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755233 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755221 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755176 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755237 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - root - INFO - Article 40755189 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - root - INFO - Article 40755232 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755254 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755245 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755140 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755138 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755151 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755180 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755060 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755185 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755133 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755048 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755032 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755084 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754998 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755024 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755044 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - root - INFO - Article 40754983 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754943 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754936 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754991 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40755051 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754895 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754890 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754893 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754875 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754906 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:04 - root - INFO - Article 40754870 did not match PICO standard. Skipping.
2025-08-05 21:20:04 - root - INFO - Successfully saved 61186 PMIDs to 'retrieved_pmids.csv'.
2025-08-05 21:20:04 - root - INFO - Updated master PMID list with 600 new PMIDs.
2025-08-05 21:20:04 - root - INFO - Screening finished for this batch. Total matching articles so far: 943
2025-08-05 21:20:04 - root - WARNING - Found only 943 studies, which is below the minimum of 3000. Refining PubMed query for next attempt...
2025-08-05 21:20:04 - modules.gemini_client - WARNING - Refining failed PubMed query: (("optic disc"[tiab] OR "optic nerve head"[tiab] OR "retinal nerve fiber layer"[tiab] OR "macular thickness"[tiab] OR "central corneal thickness"[tiab] OR "intraocular pressure"[tiab] OR "refractive error"[tiab] OR "visual field"[tiab] OR "fundus parameters"[tiab] OR "retinal parameters"[tiab] OR "cup-to-disc ratio"[tiab] OR "retinal vasculature"[tiab] OR "macular volume"[tiab] OR "foveal avascular zone"[tiab] OR "anterior chamber depth"[tiab] OR "axial length"[tiab] OR "corneal curvature"[tiab] OR "visual acuity"[tiab] OR "ophthalmic parameters"[tiab] OR "glaucoma"[mesh] OR "diabetic retinopathy"[mesh] OR "macular degeneration"[mesh] OR "retinal diseases"[mesh] OR "corneal diseases"[mesh] OR "refractive errors"[mesh] OR "glaucoma"[tiab] OR "diabetic retinopathy"[tiab] OR "macular degeneration"[tiab] OR "retinal diseases"[tiab] OR "corneal diseases"[tiab]) AND ("population-based study"[tiab] OR cohort[mesh] OR "epidemiologic studies"[mesh] OR "large sample size"[tiab] OR "general population"[tiab] OR "multi-center study"[tiab] OR "nationwide study"[tiab] OR "community-based study"[tiab] OR "massive dataset"[tiab] OR "national health and nutrition examination survey"[tiab] OR "global burden of disease"[tiab] OR "representative sample"[tiab] OR "large-scale data"[tiab] OR "healthy population"[tiab] OR "unselected population"[tiab] OR "prevalence study"[tiab] OR "screening study"[tiab] OR "observational study"[tiab] OR "cross-sectional study"[tiab] OR "cohort study"[tiab] OR "large cohort"[tiab] OR "large population"[tiab]) AND ("outlier detection"[tiab] OR "outlier analysis"[tiab] OR outlier[mesh] OR "abnormal values"[tiab] OR "normal range"[tiab] OR "reference interval"[tiab] OR "confidence interval"[tiab] OR percentile[mesh] OR "normative data"[tiab] OR "statistical outlier"[tiab] OR "normal limits"[tiab] OR "extreme values"[tiab] OR IQR[tiab] OR "interquartile range"[tiab] OR SD[tiab] OR "standard deviation"[tiab] OR "Z-score"[tiab] OR IQR[mesh] OR "sigma limits"[tiab] OR "thresholding"[tiab] OR "statistical significance"[tiab] OR "reference standard"[tiab] OR "reference population"[tiab] OR "diagnostic criteria"[tiab] OR "upper limit of normal"[tiab] OR "lower limit of normal"[tiab] OR "boundary values"[tiab] OR "deviating values"[tiab] OR "cut-off points"[tiab] OR "aberrant values"[tiab] OR "statistical distribution"[tiab] OR "distribution analysis"[tiab] OR "data exploration"[tiab] OR "reference range determination"[tiab] OR "z-score"[mesh] OR "confidence interval"[mesh] OR "normal distribution"[mesh] OR "threshold determination"[tiab] OR "statistical threshold"[tiab] OR "upper limit"[tiab] OR "lower limit"[tiab] OR "reference value calculation"[tiab] OR "deviation analysis"[tiab] OR "extreme value analysis"[tiab] OR "non-normative"[tiab] OR "statistically significant outliers"[tiab] OR "population norms"[tiab] OR "normative values"[tiab] OR "percentile analysis"[tiab] OR "distribution fitting"[tiab] OR "statistical modeling"[tiab] OR "data mining"[tiab] OR "feature extraction"[tiab]) AND ("observational study"[publication type] OR "cross-sectional studies"[mesh] OR "cohort studies"[mesh] OR "descriptive study"[mesh] OR "longitudinal study"[mesh] OR "prevalence"[mesh] OR "incidence"[mesh] OR "population study"[mesh] OR "epidemiology"[mesh] OR "analysis"[tiab] OR "method"[tiab] OR "statistics"[tiab] OR "computational analysis"[tiab] OR "statistical modeling"[tiab] OR "data mining"[tiab] OR "feature extraction"[tiab] OR "reference value"[tiab] OR "population norms"[tiab] OR "statistical analysis"[mesh] OR "data analysis"[mesh] OR "biostatistics"[mesh] OR "methodology"[mesh] OR "analytical study"[publication type] OR "comparative study"[publication type] OR "validation study"[publication type] OR "quantitative analysis"[tiab] OR "statistical methods"[tiab] OR "data evaluation"[tiab] OR "statistical distribution analysis"[tiab] OR "normative data derivation"[tiab] OR "reference value estimation"[tiab] OR "outlier identification"[tiab] OR "abnormality detection"[tiab] OR "statistical thresholding"[tiab] OR "clinical reference ranges"[tiab] OR "population reference values"[tiab] OR "distribution parameters"[tiab] OR "statistical inference"[tiab]) AND human[mesh] AND ("randomized controlled trial"[publication type] OR RCT[publication type] OR "clinical trial"[publication type] OR "comparative study"[publication type] OR "validation study"[publication type] OR "analytical study"[publication type] OR "multicenter study"[publication type] OR "large cohort"[tiab] OR "large population"[tiab] OR "population-based study"[tiab] OR "cross-sectional"[tiab] OR "cohort"[tiab] OR "observational"[tiab] OR "epidemiologic"[tiab] OR "prevalence"[tiab] OR "incidence"[tiab] OR "screening"[tiab] OR "national survey"[tiab] OR "multi-center"[tiab] OR "general population"[tiab] OR "representative sample"[tiab] OR "large-scale"[tiab] OR "unselected population"[tiab]) NOT ("review"[publication type] OR "editorial"[publication type] OR "letter"[publication type] OR "case report"[publication type] OR "comment"[publication type] OR "guideline"[publication type] OR "practice guideline"[publication type] OR "conference abstract"[publication type] OR "brief report"[publication type] OR "historical article"[publication type] OR "meta-analysis"[publication type] OR "systematic review"[publication type] OR "meta-analysis"[mesh] OR "systematic review"[mesh] OR "editorial"[mesh] OR "letter"[mesh] OR "case reports"[mesh] OR "comment"[mesh] OR "guidelines"[mesh] OR "practice guideline"[mesh] OR "conference abstract"[mesh] OR "brief report"[mesh] OR "historical article"[mesh] OR "simulation"[tiab] OR "animal"[mesh] OR "in vitro"[tiab] OR "in silico"[tiab])
2025-08-05 21:20:04 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:08 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:08 - root - INFO - Search attempt 200/299 with query: (("optic disc"[tiab] OR "optic nerve head"[tiab] OR "retinal nerve fiber layer"[tiab] OR "macular thickness"[tiab] OR "central corneal thickness"[tiab] OR "intraocular pressure"[tiab] OR "refractive error"[tiab] OR "visual field"[tiab] OR "fundus parameters"[tiab] OR "retinal parameters"[tiab] OR "cup-to-disc ratio"[tiab] OR "retinal vasculature"[tiab] OR "macular volume"[tiab] OR "foveal avascular zone"[tiab] OR "anterior chamber depth"[tiab] OR "axial length"[tiab] OR "corneal curvature"[tiab] OR "visual acuity"[tiab] OR "ophthalmic parameters"[tiab] OR "glaucoma"[mesh] OR "diabetic retinopathy"[mesh] OR "macular degeneration"[mesh] OR "retinal diseases"[mesh] OR "corneal diseases"[mesh] OR "refractive errors"[mesh]) AND ("population-based study"[tiab] OR cohort[mesh] OR "epidemiologic studies"[mesh] OR "large sample size"[tiab] OR "general population"[tiab] OR "multi-center study"[tiab] OR "nationwide study"[tiab] OR "community-based study"[tiab] OR "massive dataset"[tiab] OR "national health and nutrition examination survey"[tiab] OR "global burden of disease"[tiab] OR "representative sample"[tiab] OR "large-scale data"[tiab] OR "healthy population"[tiab] OR "unselected population"[tiab] OR "prevalence study"[tiab] OR "screening study"[tiab] OR "observational study"[tiab] OR "cross-sectional study"[tiab] OR "cohort study"[tiab] OR "large cohort"[tiab] OR "large population"[tiab]) AND ("outlier detection"[tiab] OR "outlier analysis"[tiab] OR outlier[mesh] OR "abnormal values"[tiab] OR "normal range"[tiab] OR "reference interval"[tiab] OR "confidence interval"[tiab] OR percentile[mesh] OR "normative data"[tiab] OR "statistical outlier"[tiab] OR "normal limits"[tiab] OR "extreme values"[tiab] OR IQR[tiab] OR "interquartile range"[tiab] OR SD[tiab] OR "standard deviation"[tiab] OR "Z-score"[tiab] OR IQR[mesh] OR "sigma limits"[tiab] OR "thresholding"[tiab] OR "statistical significance"[tiab] OR "reference standard"[tiab] OR "reference population"[tiab] OR "diagnostic criteria"[tiab] OR "upper limit of normal"[tiab] OR "lower limit of normal"[tiab] OR "boundary values"[tiab] OR "deviating values"[tiab] OR "cut-off points"[tiab] OR "aberrant values"[tiab] OR "statistical distribution"[tiab] OR "distribution analysis"[tiab] OR "data exploration"[tiab] OR "reference range determination"[tiab] OR "z-score"[mesh] OR "confidence interval"[mesh] OR "normal distribution"[mesh] OR "threshold determination"[tiab] OR "statistical threshold"[tiab] OR "upper limit"[tiab] OR "lower limit"[tiab] OR "reference value calculation"[tiab] OR "deviation analysis"[tiab] OR "extreme value analysis"[tiab] OR "non-normative"[tiab] OR "statistically significant outliers"[tiab] OR "population norms"[tiab] OR "normative values"[tiab] OR "percentile analysis"[tiab] OR "distribution fitting"[tiab] OR "statistical modeling"[tiab] OR "data mining"[tiab] OR "feature extraction"[tiab]) AND ("randomized controlled trial"[publication type] OR RCT[publication type] OR "clinical trial"[publication type] OR "comparative study"[publication type] OR "validation study"[publication type] OR "analytical study"[publication type] OR "multicenter study"[publication type] OR "large cohort"[tiab] OR "large population"[tiab] OR "population-based study"[tiab] OR "cross-sectional"[tiab] OR "cohort"[tiab] OR "observational"[tiab] OR "epidemiologic"[tiab] OR "prevalence"[tiab] OR "incidence"[tiab] OR "screening"[tiab] OR "national survey"[tiab] OR "multi-center"[tiab] OR "general population"[tiab] OR "representative sample"[tiab] OR "large-scale"[tiab] OR "unselected population"[tiab] OR "quantitative analysis"[tiab] OR "statistical analysis"[mesh] OR "data analysis"[mesh] OR "biostatistics"[mesh] OR "methodology"[mesh] OR "analytical study"[publication type] OR "comparative study"[publication type] OR "validation study"[publication type] OR "quantitative analysis"[tiab] OR "statistical methods"[tiab] OR "data evaluation"[tiab] OR "statistical distribution analysis"[tiab] OR "normative data derivation"[tiab] OR "reference value estimation"[tiab] OR "outlier identification"[tiab] OR "abnormality detection"[tiab] OR "statistical thresholding"[tiab] OR "clinical reference ranges"[tiab] OR "population reference values"[tiab] OR "distribution parameters"[tiab] OR "statistical inference"[tiab]) AND human[mesh] NOT ("review"[publication type] OR "editorial"[publication type] OR "letter"[publication type] OR "case report"[publication type] OR "comment"[publication type] OR "guideline"[publication type] OR "practice guideline"[publication type] OR "conference abstract"[publication type] OR "brief report"[publication type] OR "historical article"[publication type] OR "meta-analysis"[publication type] OR "systematic review"[publication type] OR "meta-analysis"[mesh] OR "systematic review"[mesh] OR "editorial"[mesh] OR "letter"[mesh] OR "case reports"[mesh] OR "comment"[mesh] OR "guidelines"[mesh] OR "practice guideline"[mesh] OR "conference abstract"[mesh] OR "brief report"[mesh] OR "historical article"[mesh] OR "simulation"[tiab] OR "animal"[mesh] OR "in vitro"[tiab] OR "in silico"[tiab])
2025-08-05 21:20:08 - root - INFO - Phase 2 (Round 200): Adaptive strategy based on 943 existing results
2025-08-05 21:20:08 - modules.pubmed_client - WARNING - Query length (5120) exceeds recommended limit. Truncating...
2025-08-05 21:20:08 - modules.pubmed_client - WARNING - Truncated query: (("optic disc"[tiab] OR "optic nerve head"[tiab] OR "retinal nerve fiber layer"[tiab] OR "macular thickness"[tiab] OR "central corneal thickness"[tiab] OR "intraocular pressure"[tiab] OR "refractive error"[tiab] OR "visual field"[tiab] OR "fundus parameters"[tiab] OR "retinal parameters"[tiab] OR "cup-to-disc ratio"[tiab] OR "retinal vasculature"[tiab] OR "macular volume"[tiab] OR "foveal avascular zone"[tiab] OR "anterior chamber depth"[tiab] OR "axial length"[tiab] OR "corneal curvature"[tiab] OR "visual acuity"[tiab] OR "ophthalmic parameters"[tiab] OR "glaucoma"[mesh] OR "diabetic retinopathy"[mesh] OR "macular degeneration"[mesh] OR "retinal diseases"[mesh] OR "corneal diseases"[mesh] OR "refractive errors"[mesh]) AND ("population-based study"[tiab] OR cohort[mesh] OR "epidemiologic studies"[mesh] OR "large sample size"[tiab] OR "general population"[tiab] OR "multi-center study"[tiab] OR "nationwide study"[tiab] OR "community-based study"[tiab] OR "massive dataset"[tiab] OR "national health and nutrition examination survey"[tiab] OR "global burden of disease"[tiab] OR "representative sample"[tiab] OR "large-scale data"[tiab] OR "healthy population"[tiab] OR "unselected population"[tiab] OR "prevalence study"[tiab] OR "screening study"[tiab] OR "observational study"[tiab] OR "cross-sectional study"[tiab] OR "cohort study"[tiab] OR "large cohort"[tiab] OR "large population"[tiab]) AND ("outlier detection"[tiab] OR "outlier analysis"[tiab] OR outlier[mesh] OR "abnormal values"[tiab] OR "normal range"[tiab] OR "reference interval"[tiab] OR "confidence interval"[tiab] OR percentile[mesh] OR "normative data"[tiab] OR "statistical outlier"[tiab] OR "normal limits"[tiab] OR "extreme values"[tiab] OR IQR[tiab] OR "interquartile range"[tiab] OR SD[tiab] OR "standard deviation"[tiab] OR "Z-score"[tiab] OR IQR[mesh] OR "sigma limits"[tiab] OR "thresholding"[tiab] OR "statistical significance"[tiab] OR "reference standard"[tiab] OR "reference population"[tiab] OR "diagnostic criteria"[tiab] OR "upper limit of normal"[tiab] OR "lower limit of normal"[tiab] OR "boundary values"[tiab] OR "deviating values"[tiab] OR "cut-off points"[tiab] OR "aberrant values"[tiab] OR "statistical distribution"[tiab] OR "distribution analysis"[tiab] OR "data exploration"[tiab] OR "reference range determination"[tiab] OR "z-score"[mesh] OR "confidence interval"[mesh] OR "normal distribution"[mesh] OR "threshold determination"[tiab] OR "statistical threshold"[tiab] OR "upper limit"[tiab] OR "lower limit"[tiab] OR "reference value calculation"[tiab] OR "deviation analysis"[tiab] OR "extreme value analysis"[tiab] OR "non-normative"[tiab] OR "statistically significant outliers"[tiab] OR "population norms"[tiab] OR "normative values"[tiab] OR "percentile analysis"[tiab] OR "distribution fitting"[tiab] OR "statistical modeling"[tiab] OR "data mining"[tiab] OR "feature extraction"[tiab]) AND ("randomized controlled trial"[publication type] OR RCT[publication type] OR "clinical trial"[publication type] OR "comparative study"[publication type] OR "validation study"[publication type] OR "analytical study"[publication type] OR "multicenter study"[publication type] OR "large cohort"[tiab] OR "large population"[tiab] OR "population-based study"[tiab] OR "cross-sectional"[tiab] OR "cohort"[tiab] OR "observational"[tiab] OR "epidemiologic"[tiab] OR "prevalence"[tiab] OR "incidence"[tiab] OR "screening"[tiab] OR "national survey"[tiab] OR "multi-center"[tiab] OR "general population"[tiab] OR "representative sample"[tiab] OR "large-scale"[tiab] OR "unselected population"[tiab] OR "quantitative analysis"[tiab] OR "statistical analysis"[mesh] OR "data analysis"[mesh] OR "biostatistics"[mesh] OR "methodology"[mesh] OR "analytical study"[publication type] OR "comparative study"[publication type] OR "validation study"[publication type] OR "quantitative analysis"[tiab] OR "statistical methods"[tiab] OR "data evaluation"[tiab] OR "statistical distribution analysis"[tiab] OR "norm
2025-08-05 21:20:08 - modules.pubmed_client - INFO - Executing Esearch for term: (("optic disc"[tiab] OR "optic nerve head"[tiab] OR "retinal nerve fiber layer"[tiab] OR "macular th... (retmax=100000)
2025-08-05 21:20:09 - modules.pubmed_client - INFO - Esearch found 6871278 articles, retrieved 9999 PMIDs.
2025-08-05 21:20:09 - root - INFO - Search successful! Found 6871278 articles.
2025-08-05 21:20:09 - root - INFO - Retrieved 9999 PMIDs from search result.
2025-08-05 21:20:09 - root - INFO - Found 4745 new PMIDs after excluding already processed ones.
2025-08-05 21:20:09 - root - INFO - Selecting first 600 PMIDs for detailed fetching.
2025-08-05 21:20:09 - modules.pubmed_client - INFO - Executing Efetch for 600 specific PMIDs using POST request
2025-08-05 21:20:12 - modules.pubmed_client - INFO - Efetch by PMIDs completed successfully.
2025-08-05 21:20:12 - modules.data_processor - INFO - Parsing PubMed XML data...
2025-08-05 21:20:13 - modules.data_processor - INFO - Successfully parsed 600 articles from XML.
2025-08-05 21:20:13 - root - INFO - Successfully fetched and parsed 600 articles from PubMed.
2025-08-05 21:20:13 - root - INFO - Starting concurrent screening of 600 new articles...
2025-08-05 21:20:13 - root - INFO - Screening strategy: Strict PICO (Phase 2, 943 results - sufficient)
2025-08-05 21:20:13 - root - WARNING - Skipping article 40762920 due to missing abstract.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762326 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762247 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762720 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761845 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761818 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - INFO - Article 40762325 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - INFO - Article 40762615 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762457 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - INFO - Article 40762474 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762481 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762286 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762795 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - WARNING - Skipping article 40760968 due to missing abstract.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762127 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - root - INFO - Article 40762276 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762406 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40762517 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761625 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761630 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761577 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761606 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761462 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - INFO - Article 40761473 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761952 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761475 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761393 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761307 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761207 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - INFO - Article 40761868 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761308 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761221 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40760953 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - WARNING - Skipping article 40759413 due to missing abstract.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40760928 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40761348 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - root - INFO - Article 40760903 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40760899 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40760841 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40760766 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - INFO - Article 40760803 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40760197 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759787 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759703 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40760083 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40760303 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759660 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - WARNING - Skipping article 40758549 due to missing abstract.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759497 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - INFO - Article 40759561 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - root - WARNING - Skipping article 40758374 due to missing abstract.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759488 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - INFO - Article 40759412 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - WARNING - Skipping article 40758363 due to missing abstract.
2025-08-05 21:20:13 - root - WARNING - Skipping article 40758355 due to missing abstract.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - root - WARNING - Skipping article 40758323 due to missing abstract.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40760115 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - WARNING - Skipping article 40758181 due to missing abstract.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759496 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759100 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759255 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759449 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759230 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40758981 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - root - INFO - Article 40758859 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:13 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:13 - root - INFO - Article 40759003 did not match PICO standard. Skipping.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:13 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758746 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - root - INFO - Article 40758733 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758373 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758433 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758716 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758141 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758547 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758686 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758003 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758276 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - root - INFO - Article 40758331 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - root - INFO - Article 40757906 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758103 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758776 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40758647 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - root - INFO - Article 40757751 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757773 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757785 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757761 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757288 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757192 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757685 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - root - INFO - Article 40757674 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757385 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - root - INFO - Article 40757138 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757215 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757940 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757093 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40756819 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757126 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40756799 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40756774 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40757004 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - root - INFO - Article 40757020 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - root - INFO - Article 40757073 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - root - INFO - Article 40756691 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40756885 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40756802 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - root - INFO - Article 40756629 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - root - INFO - Article 40756689 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - root - INFO - Article 40756647 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40756672 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - root - INFO - Article 40756600 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - root - INFO - Article 40756598 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - root - INFO - Article 40756584 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - root - INFO - Article 40756653 did not match PICO standard. Skipping.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:14 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:15 - root - INFO - Article 40756576 did not match PICO standard. Skipping.
2025-08-05 21:20:15 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:15 - root - INFO - Article 40757061 did not match PICO standard. Skipping.
2025-08-05 21:20:15 - root - INFO - Article 40756552 did not match PICO standard. Skipping.
2025-08-05 21:20:15 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:15 - root - INFO - Article 40756698 did not match PICO standard. Skipping.
2025-08-05 21:20:15 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:15 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:15 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:15 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:15 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:15 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:15 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:16 - root - INFO - Article 40756541 did not match PICO standard. Skipping.
2025-08-05 21:20:16 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:16 - root - INFO - Article 40756542 did not match PICO standard. Skipping.
2025-08-05 21:20:16 - root - INFO - Article 40756537 did not match PICO standard. Skipping.
2025-08-05 21:20:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:16 - root - ERROR - Failed to decode JSON from Gemini response for article 40756592. Original response: {}
```json
{
  "研究目的": "探究晚睡型人格、手机依赖恐惧症（nomophobia）和错失恐惧症（FoMO）与睡眠质量之间的关系，以及手机依赖恐惧症在晚睡型人格与睡眠质量以及错失恐惧症与睡眠质量关系中的中介作用。",
  "研究类型": "横断面调查研究",
  "研究方法": "通过问卷调查收集数据，使用Jamovi和SPSS进行统计分析，包括中介效应分析。",
  "研究对象...
2025-08-05 21:20:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:16 - root - ERROR - Cleaned response: {}
```json
{
  "研究目的": "探究晚睡型人格、手机依赖恐惧症（nomophobia）和错失恐惧症（FoMO）与睡眠质量之间的关系，以及手机依赖恐惧症在晚睡型人格与睡眠质量以及错失恐惧症与睡眠质量关系中的中介作用。",
  "研究类型": "横断面调查研究",
  "研究方法": "通过问卷调查收集数据，使用Jamovi和SPSS进行统计分析，包括中介效应分析。",
  "研究对象...
2025-08-05 21:20:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:16 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:16 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:18 - root - INFO - Article 40756536 did not match PICO standard. Skipping.
2025-08-05 21:20:18 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:18 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:18 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:18 - root - INFO - Article 40756528 did not match PICO standard. Skipping.
2025-08-05 21:20:18 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:18 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756498 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756521 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756510 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756472 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756457 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756293 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756447 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756300 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756373 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756254 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - root - INFO - Article 40756229 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - root - INFO - Article 40756226 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - root - INFO - Article 40756413 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756198 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756182 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:19 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:19 - root - INFO - Article 40756168 did not match PICO standard. Skipping.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:19 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:20 - root - INFO - Article 40756153 did not match PICO standard. Skipping.
2025-08-05 21:20:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:20 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:20 - root - INFO - Article 40756149 did not match PICO standard. Skipping.
2025-08-05 21:20:20 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:20 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:21 - root - INFO - Article 40756124 did not match PICO standard. Skipping.
2025-08-05 21:20:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:21 - root - INFO - Article 40756116 did not match PICO standard. Skipping.
2025-08-05 21:20:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:21 - root - INFO - Article 40756103 did not match PICO standard. Skipping.
2025-08-05 21:20:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:21 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:21 - root - INFO - Article 40756094 did not match PICO standard. Skipping.
2025-08-05 21:20:21 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:21 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:22 - root - INFO - Article 40756037 did not match PICO standard. Skipping.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:22 - root - INFO - Article 40756056 did not match PICO standard. Skipping.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:22 - root - INFO - Article 40756047 did not match PICO standard. Skipping.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:22 - root - INFO - Article 40756024 did not match PICO standard. Skipping.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:22 - root - INFO - Article 40756000 did not match PICO standard. Skipping.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:22 - root - INFO - Article 40755994 did not match PICO standard. Skipping.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:22 - root - INFO - Article 40755968 did not match PICO standard. Skipping.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:22 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:22 - root - INFO - Article 40755945 did not match PICO standard. Skipping.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:22 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:23 - root - INFO - Article 40755908 did not match PICO standard. Skipping.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:23 - root - INFO - Article 40755888 did not match PICO standard. Skipping.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:23 - root - INFO - Article 40755905 did not match PICO standard. Skipping.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:23 - root - INFO - Article 40755876 did not match PICO standard. Skipping.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:23 - root - INFO - Article 40755861 did not match PICO standard. Skipping.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:23 - root - INFO - Article 40755855 did not match PICO standard. Skipping.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:23 - root - INFO - Article 40755859 did not match PICO standard. Skipping.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:23 - root - INFO - Article 40755818 did not match PICO standard. Skipping.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:23 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:23 - root - INFO - Article 40755741 did not match PICO standard. Skipping.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:23 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:24 - root - INFO - Article 40755732 did not match PICO standard. Skipping.
2025-08-05 21:20:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:24 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:24 - root - INFO - Article 40755717 did not match PICO standard. Skipping.
2025-08-05 21:20:24 - root - INFO - Article 40755705 did not match PICO standard. Skipping.
2025-08-05 21:20:24 - root - WARNING - Skipping article 40755413 due to missing abstract.
2025-08-05 21:20:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:24 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:24 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:25 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:25 - root - INFO - Article 40755704 did not match PICO standard. Skipping.
2025-08-05 21:20:25 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:25 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:27 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:27 - root - INFO - Article 40755671 did not match PICO standard. Skipping.
2025-08-05 21:20:27 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:27 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:30 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:30 - root - INFO - Article 40755649 did not match PICO standard. Skipping.
2025-08-05 21:20:30 - root - INFO - Article 40755614 did not match PICO standard. Skipping.
2025-08-05 21:20:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:30 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:30 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:34 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:34 - root - INFO - Article 40755602 did not match PICO standard. Skipping.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:34 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:34 - root - INFO - Article 40755598 did not match PICO standard. Skipping.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:34 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:34 - root - INFO - Article 40755575 did not match PICO standard. Skipping.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:34 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:34 - root - INFO - Article 40755574 did not match PICO standard. Skipping.
2025-08-05 21:20:34 - root - WARNING - Skipping article 40755118 due to missing abstract.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:34 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:34 - root - INFO - Article 40755549 did not match PICO standard. Skipping.
2025-08-05 21:20:34 - root - INFO - Article 40755554 did not match PICO standard. Skipping.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:34 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:37 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:37 - root - INFO - Article 40755545 did not match PICO standard. Skipping.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:37 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:37 - root - INFO - Article 40755530 did not match PICO standard. Skipping.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:37 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:37 - root - INFO - Article 40755522 did not match PICO standard. Skipping.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:37 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:37 - root - INFO - Article 40755512 did not match PICO standard. Skipping.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:37 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:37 - root - INFO - Article 40755508 did not match PICO standard. Skipping.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:37 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:38 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:38 - root - INFO - Article 40755493 did not match PICO standard. Skipping.
2025-08-05 21:20:38 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:38 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:38 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:38 - root - INFO - Article 40755472 did not match PICO standard. Skipping.
2025-08-05 21:20:38 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:38 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:38 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:38 - root - INFO - Article 40755485 did not match PICO standard. Skipping.
2025-08-05 21:20:38 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:38 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:38 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:38 - root - INFO - Article 40755402 did not match PICO standard. Skipping.
2025-08-05 21:20:38 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:38 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755396 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755376 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755394 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755261 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755132 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755224 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755121 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755105 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - root - INFO - Article 40755190 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755100 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - root - INFO - Article 40755085 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755067 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755052 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755038 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755014 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40755009 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40754957 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40754888 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - root - WARNING - Skipping article 40754733 due to missing abstract.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40754916 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:39 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:39 - root - INFO - Article 40754953 did not match PICO standard. Skipping.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:39 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754857 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754846 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754845 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754838 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754829 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754788 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754828 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - root - INFO - Article 40754800 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754786 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754783 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754778 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754773 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:40 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:40 - root - INFO - Article 40754756 did not match PICO standard. Skipping.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:40 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:41 - root - INFO - Article 40754748 did not match PICO standard. Skipping.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:41 - root - INFO - Article 40754745 did not match PICO standard. Skipping.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:41 - root - INFO - Article 40754739 did not match PICO standard. Skipping.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:41 - root - INFO - Article 40754711 did not match PICO standard. Skipping.
2025-08-05 21:20:41 - root - WARNING - Skipping article 40754452 due to missing abstract.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:41 - root - INFO - Article 40754738 did not match PICO standard. Skipping.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:41 - root - INFO - Article 40754710 did not match PICO standard. Skipping.
2025-08-05 21:20:41 - root - INFO - Article 40754692 did not match PICO standard. Skipping.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:41 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:42 - root - INFO - Article 40754678 did not match PICO standard. Skipping.
2025-08-05 21:20:42 - root - INFO - Article 40754677 did not match PICO standard. Skipping.
2025-08-05 21:20:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:42 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:42 - root - INFO - Article 40754670 did not match PICO standard. Skipping.
2025-08-05 21:20:42 - modules.gemini_client - INFO - Sending abstract to Gemini for screening and extraction based on complete PICO document.
2025-08-05 21:20:42 - modules.gemini_client - INFO - Sending prompt to Gemini API...
2025-08-05 21:20:45 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:45 - root - INFO - Article 40754663 did not match PICO standard. Skipping.
2025-08-05 21:20:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:46 - root - INFO - Article 40754650 did not match PICO standard. Skipping.
2025-08-05 21:20:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:46 - root - INFO - Article 40754641 did not match PICO standard. Skipping.
2025-08-05 21:20:46 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:46 - root - INFO - Article 40754640 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - root - INFO - Article 40754605 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - root - INFO - Article 40754568 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - root - INFO - Article 40754571 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - root - INFO - Article 40754555 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - root - INFO - Article 40754500 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - root - INFO - Article 40754544 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - root - INFO - Article 40754463 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - root - INFO - Article 40754476 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - root - INFO - Article 40754475 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - root - INFO - Article 40754450 did not match PICO standard. Skipping.
2025-08-05 21:20:47 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:47 - root - INFO - Article 40754427 did not match PICO standard. Skipping.
2025-08-05 21:20:48 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:48 - root - INFO - Article 40754419 did not match PICO standard. Skipping.
2025-08-05 21:20:48 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:48 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:48 - root - INFO - Article 40754406 did not match PICO standard. Skipping.
2025-08-05 21:20:48 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:48 - root - INFO - Article 40754418 did not match PICO standard. Skipping.
2025-08-05 21:20:48 - modules.gemini_client - INFO - Successfully received response from Gemini API.
2025-08-05 21:20:48 - root - INFO - Article 40754397 did not match PICO standard. Skipping.
2025-08-05 21:20:48 - root - INFO - Article 40754368 did not match PICO standard. Skipping.
